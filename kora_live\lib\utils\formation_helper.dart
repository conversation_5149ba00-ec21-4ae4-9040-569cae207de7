import '../models/formation_model.dart';

// مساعد لتحويل بيانات التشكيلة إلى مواقع على الملعب
class FormationHelper {
  // تحويل نموذج التشكيلة إلى قائمة مواقع للعرض
  static List<Map<String, dynamic>> convertToPositions(FormationModel formation) {
    List<Map<String, dynamic>> players = [];
    
    // مواقع الخطوط على الملعب (Y coordinates)
    final linePositions = {
      'حارس': 0.85,
      'دفاع': 0.65,
      'وسط': 0.45,
      'هجوم': 0.25,
    };
    
    // معالجة كل خط
    formation.lines.forEach((lineKey, lineData) {
      final linePlayers = lineData.players;
      final lineY = linePositions[linePlayers.first.line] ?? 0.5;
      
      // حساب المواقع الأفقية حسب عدد اللاعبين في الخط
      for (int i = 0; i < linePlayers.length; i++) {
        final player = linePlayers[i];
        
        // حساب الموقع الأفقي (X coordinate)
        double x = _calculateXPosition(linePlayers.length, i);
        
        // إضافة اللاعب مع موقعه
        players.add({
          'name': player.name,
          'number': player.number,
          'rating': player.rating,
          'position': player.position,
          'line': player.line,
          'linePosition': player.linePosition,
          'ratingColor': player.ratingColor,
          'x': x,
          'y': lineY,
        });
      }
    });
    
    return players;
  }

  // حساب الموقع الأفقي للاعب
  static double _calculateXPosition(int totalPlayers, int playerIndex) {
    switch (totalPlayers) {
      case 1:
        // لاعب واحد في الوسط
        return 0.5;
      case 2:
        // لاعبان
        return [0.3, 0.7][playerIndex];
      case 3:
        // ثلاثة لاعبين
        return [0.2, 0.5, 0.8][playerIndex];
      case 4:
        // أربعة لاعبين
        return [0.15, 0.4, 0.6, 0.85][playerIndex];
      case 5:
        // خمسة لاعبين
        return [0.1, 0.3, 0.5, 0.7, 0.9][playerIndex];
      default:
        // توزيع متساوي للأعداد الأخرى
        return (playerIndex + 1) / (totalPlayers + 1);
    }
  }

  // الحصول على لون التقييم
  static String getRatingColor(double rating) {
    if (rating >= 8.0) return 'green';
    if (rating >= 7.0) return 'blue';
    if (rating >= 6.5) return 'yellow';
    if (rating >= 6.0) return 'orange';
    return 'red';
  }

  // تحديث تقييم لاعب
  static FormationModel updatePlayerRating(
    FormationModel formation,
    String playerNumber,
    String newRating,
  ) {
    final updatedLines = <String, LineData>{};
    
    formation.lines.forEach((lineKey, lineData) {
      final updatedPlayers = lineData.players.map((player) {
        if (player.number == playerNumber) {
          return PlayerData(
            name: player.name,
            number: player.number,
            rating: newRating,
            position: player.position,
            line: player.line,
            linePosition: player.linePosition,
            ratingColor: getRatingColor(double.parse(newRating)),
          );
        }
        return player;
      }).toList();
      
      updatedLines[lineKey] = LineData(players: updatedPlayers);
    });
    
    return FormationModel(
      formation: formation.formation,
      team: formation.team,
      lines: updatedLines,
    );
  }

  // تغيير تشكيل الفريق
  static FormationModel changeFormation(
    FormationModel currentFormation,
    String newFormation,
  ) {
    // هذه دالة مبسطة - في التطبيق الحقيقي ستحتاج لمنطق أكثر تعقيداً
    switch (newFormation) {
      case '4-4-2':
        return _convertTo442(currentFormation);
      case '3-5-2':
        return _convertTo352(currentFormation);
      case '4-3-3':
        return _convertTo433(currentFormation);
      default:
        return currentFormation;
    }
  }

  // تحويل إلى تشكيل 4-4-2
  static FormationModel _convertTo442(FormationModel formation) {
    // منطق تحويل التشكيل - مبسط للمثال
    return FormationModel(
      formation: '4-4-2',
      team: formation.team,
      lines: formation.lines, // في التطبيق الحقيقي ستحتاج لإعادة ترتيب اللاعبين
    );
  }

  // تحويل إلى تشكيل 3-5-2
  static FormationModel _convertTo352(FormationModel formation) {
    return FormationModel(
      formation: '3-5-2',
      team: formation.team,
      lines: formation.lines,
    );
  }

  // تحويل إلى تشكيل 4-3-3
  static FormationModel _convertTo433(FormationModel formation) {
    return FormationModel(
      formation: '4-3-3',
      team: formation.team,
      lines: formation.lines,
    );
  }

  // الحصول على إحصائيات التشكيلة
  static Map<String, dynamic> getFormationStats(FormationModel formation) {
    int totalPlayers = 0;
    double totalRating = 0;
    Map<String, int> lineCount = {};
    
    formation.lines.forEach((lineKey, lineData) {
      lineCount[lineData.players.first.line] = lineData.players.length;
      
      for (var player in lineData.players) {
        totalPlayers++;
        totalRating += double.parse(player.rating);
      }
    });
    
    return {
      'formation': formation.formation,
      'team': formation.team,
      'totalPlayers': totalPlayers,
      'averageRating': (totalRating / totalPlayers).toStringAsFixed(1),
      'lineCount': lineCount,
      'strongestLine': _getStrongestLine(formation),
      'weakestLine': _getWeakestLine(formation),
    };
  }

  // الحصول على أقوى خط
  static String _getStrongestLine(FormationModel formation) {
    double bestAverage = 0;
    String bestLine = '';
    
    formation.lines.forEach((lineKey, lineData) {
      double lineTotal = 0;
      for (var player in lineData.players) {
        lineTotal += double.parse(player.rating);
      }
      double lineAverage = lineTotal / lineData.players.length;
      
      if (lineAverage > bestAverage) {
        bestAverage = lineAverage;
        bestLine = lineData.players.first.line;
      }
    });
    
    return bestLine;
  }

  // الحصول على أضعف خط
  static String _getWeakestLine(FormationModel formation) {
    double worstAverage = 10;
    String worstLine = '';
    
    formation.lines.forEach((lineKey, lineData) {
      double lineTotal = 0;
      for (var player in lineData.players) {
        lineTotal += double.parse(player.rating);
      }
      double lineAverage = lineTotal / lineData.players.length;
      
      if (lineAverage < worstAverage) {
        worstAverage = lineAverage;
        worstLine = lineData.players.first.line;
      }
    });
    
    return worstLine;
  }

  // البحث عن لاعب بالرقم
  static PlayerData? findPlayerByNumber(FormationModel formation, String number) {
    for (var lineData in formation.lines.values) {
      for (var player in lineData.players) {
        if (player.number == number) {
          return player;
        }
      }
    }
    return null;
  }

  // الحصول على لاعبي خط معين
  static List<PlayerData> getPlayersByLine(FormationModel formation, String line) {
    List<PlayerData> players = [];
    
    formation.lines.forEach((lineKey, lineData) {
      for (var player in lineData.players) {
        if (player.line == line) {
          players.add(player);
        }
      }
    });
    
    return players;
  }
}
