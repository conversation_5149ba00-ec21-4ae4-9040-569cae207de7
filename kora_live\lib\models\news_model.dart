class NewsModel {
  final String id;
  final String title;
  final String content;
  final String imageUrl;
  final String category;
  final DateTime publishedAt;
  final String author;
  final bool isBreaking;
  final int readTime; // in minutes
  final List<String> tags;

  NewsModel({
    required this.id,
    required this.title,
    required this.content,
    required this.imageUrl,
    required this.category,
    required this.publishedAt,
    required this.author,
    this.isBreaking = false,
    this.readTime = 5,
    this.tags = const [],
  });

  factory NewsModel.fromJson(Map<String, dynamic> json) {
    return NewsModel(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      content: json['content'] ?? '',
      imageUrl: json['imageUrl'] ?? '',
      category: json['category'] ?? '',
      publishedAt: DateTime.parse(json['publishedAt'] ?? DateTime.now().toIso8601String()),
      author: json['author'] ?? '',
      isBreaking: json['isBreaking'] ?? false,
      readTime: json['readTime'] ?? 5,
      tags: List<String>.from(json['tags'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'imageUrl': imageUrl,
      'category': category,
      'publishedAt': publishedAt.toIso8601String(),
      'author': author,
      'isBreaking': isBreaking,
      'readTime': readTime,
      'tags': tags,
    };
  }

  NewsModel copyWith({
    String? id,
    String? title,
    String? content,
    String? imageUrl,
    String? category,
    DateTime? publishedAt,
    String? author,
    bool? isBreaking,
    int? readTime,
    List<String>? tags,
  }) {
    return NewsModel(
      id: id ?? this.id,
      title: title ?? this.title,
      content: content ?? this.content,
      imageUrl: imageUrl ?? this.imageUrl,
      category: category ?? this.category,
      publishedAt: publishedAt ?? this.publishedAt,
      author: author ?? this.author,
      isBreaking: isBreaking ?? this.isBreaking,
      readTime: readTime ?? this.readTime,
      tags: tags ?? this.tags,
    );
  }

  @override
  String toString() {
    return 'NewsModel(id: $id, title: $title, category: $category, isBreaking: $isBreaking)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NewsModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
