import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../services/supabase_service.dart';
import '../../providers/auth_provider.dart';
import '../../providers/news_provider.dart';
import '../../providers/notification_provider.dart';
import '../../widgets/custom_button.dart';
import '../../widgets/custom_text_field.dart';

class AdminPanelScreen extends StatefulWidget {
  const AdminPanelScreen({super.key});

  @override
  State<AdminPanelScreen> createState() => _AdminPanelScreenState();
}

class _AdminPanelScreenState extends State<AdminPanelScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  
  // News form controllers
  final _newsTitleController = TextEditingController();
  final _newsContentController = TextEditingController();
  final _newsImageUrlController = TextEditingController();
  final _newsAuthorController = TextEditingController();
  final _newsTagsController = TextEditingController();
  
  // Notification form controllers
  final _notificationTitleController = TextEditingController();
  final _notificationMessageController = TextEditingController();
  
  String _selectedNewsCategory = 'الدوري المصري';
  String _selectedNotificationType = 'general';
  bool _isBreakingNews = false;
  bool _isLoading = false;

  final List<String> _newsCategories = [
    'الدوري المصري',
    'الدوري الإنجليزي',
    'الدوري الإسباني',
    'دوري أبطال أوروبا',
    'كأس العالم',
    'انتقالات',
    'عام',
  ];

  final List<String> _notificationTypes = [
    'general',
    'news',
    'match',
    'goal',
    'transfer',
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _newsTitleController.dispose();
    _newsContentController.dispose();
    _newsImageUrlController.dispose();
    _newsAuthorController.dispose();
    _newsTagsController.dispose();
    _notificationTitleController.dispose();
    _notificationMessageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    
    // التحقق من صلاحيات الإدارة (يمكن تطويرها لاحقاً)
    if (!authProvider.isLoggedIn) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('لوحة الإدارة'),
          backgroundColor: AppColors.primaryGreen,
        ),
        body: const Center(
          child: Text(
            'يجب تسجيل الدخول للوصول إلى لوحة الإدارة',
            style: TextStyle(fontSize: 18),
            textAlign: TextAlign.center,
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'لوحة الإدارة',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
        backgroundColor: AppColors.primaryGreen,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: 'إضافة خبر', icon: Icon(Icons.article)),
            Tab(text: 'إرسال إشعار', icon: Icon(Icons.notifications)),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildAddNewsTab(),
          _buildSendNotificationTab(),
        ],
      ),
    );
  }

  Widget _buildAddNewsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          const Text(
            'إضافة خبر جديد',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          
          CustomTextField(
            controller: _newsTitleController,
            labelText: 'عنوان الخبر',
            hintText: 'أدخل عنوان الخبر',
            prefixIcon: Icons.title,
          ),
          const SizedBox(height: 16),
          
          CustomTextField(
            controller: _newsContentController,
            labelText: 'محتوى الخبر',
            hintText: 'أدخل محتوى الخبر',
            prefixIcon: Icons.article,
            maxLines: 5,
          ),
          const SizedBox(height: 16),
          
          CustomTextField(
            controller: _newsImageUrlController,
            labelText: 'رابط الصورة (اختياري)',
            hintText: 'https://example.com/image.jpg',
            prefixIcon: Icons.image,
          ),
          const SizedBox(height: 16),
          
          CustomTextField(
            controller: _newsAuthorController,
            labelText: 'اسم الكاتب',
            hintText: 'أدخل اسم كاتب الخبر',
            prefixIcon: Icons.person,
          ),
          const SizedBox(height: 16),
          
          // Category dropdown
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
            decoration: BoxDecoration(
              color: AppColors.textLight,
              borderRadius: BorderRadius.circular(AppConstants.defaultRadius),
            ),
            child: DropdownButtonHideUnderline(
              child: DropdownButton<String>(
                value: _selectedNewsCategory,
                isExpanded: true,
                items: _newsCategories.map((category) {
                  return DropdownMenuItem(
                    value: category,
                    child: Text(category),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedNewsCategory = value!;
                  });
                },
              ),
            ),
          ),
          const SizedBox(height: 16),
          
          CustomTextField(
            controller: _newsTagsController,
            labelText: 'الكلمات المفتاحية (مفصولة بفواصل)',
            hintText: 'ريال مدريد, برشلونة, الكلاسيكو',
            prefixIcon: Icons.tag,
          ),
          const SizedBox(height: 16),
          
          // Breaking news checkbox
          Row(
            children: [
              Checkbox(
                value: _isBreakingNews,
                onChanged: (value) {
                  setState(() {
                    _isBreakingNews = value!;
                  });
                },
                activeColor: AppColors.primaryGreen,
              ),
              const Text('خبر عاجل'),
            ],
          ),
          const SizedBox(height: 24),
          
          CustomButton(
            text: _isLoading ? 'جاري الإضافة...' : 'إضافة الخبر',
            onPressed: _isLoading ? null : _addNews,
            backgroundColor: AppColors.primaryGreen,
            textColor: Colors.white,
          ),
        ],
      ),
    );
  }

  Widget _buildSendNotificationTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          const Text(
            'إرسال إشعار جديد',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          
          CustomTextField(
            controller: _notificationTitleController,
            labelText: 'عنوان الإشعار',
            hintText: 'أدخل عنوان الإشعار',
            prefixIcon: Icons.title,
          ),
          const SizedBox(height: 16),
          
          CustomTextField(
            controller: _notificationMessageController,
            labelText: 'رسالة الإشعار',
            hintText: 'أدخل نص الإشعار',
            prefixIcon: Icons.message,
            maxLines: 3,
          ),
          const SizedBox(height: 16),
          
          // Notification type dropdown
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
            decoration: BoxDecoration(
              color: AppColors.textLight,
              borderRadius: BorderRadius.circular(AppConstants.defaultRadius),
            ),
            child: DropdownButtonHideUnderline(
              child: DropdownButton<String>(
                value: _selectedNotificationType,
                isExpanded: true,
                items: _notificationTypes.map((type) {
                  return DropdownMenuItem(
                    value: type,
                    child: Text(_getNotificationTypeDisplayName(type)),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedNotificationType = value!;
                  });
                },
              ),
            ),
          ),
          const SizedBox(height: 24),
          
          CustomButton(
            text: _isLoading ? 'جاري الإرسال...' : 'إرسال الإشعار',
            onPressed: _isLoading ? null : _sendNotification,
            backgroundColor: AppColors.primaryGreen,
            textColor: Colors.white,
          ),
        ],
      ),
    );
  }

  String _getNotificationTypeDisplayName(String type) {
    switch (type) {
      case 'general':
        return 'عام';
      case 'news':
        return 'أخبار';
      case 'match':
        return 'مباراة';
      case 'goal':
        return 'هدف';
      case 'transfer':
        return 'انتقال';
      default:
        return type;
    }
  }

  Future<void> _addNews() async {
    if (_newsTitleController.text.trim().isEmpty ||
        _newsContentController.text.trim().isEmpty) {
      _showErrorDialog('يرجى ملء العنوان والمحتوى على الأقل');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final tags = _newsTagsController.text
          .split(',')
          .map((tag) => tag.trim())
          .where((tag) => tag.isNotEmpty)
          .toList();

      final success = await SupabaseService.addNews(
        title: _newsTitleController.text.trim(),
        content: _newsContentController.text.trim(),
        category: _selectedNewsCategory,
        imageUrl: _newsImageUrlController.text.trim().isEmpty
            ? null
            : _newsImageUrlController.text.trim(),
        author: _newsAuthorController.text.trim().isEmpty
            ? null
            : _newsAuthorController.text.trim(),
        isBreaking: _isBreakingNews,
        tags: tags.isEmpty ? null : tags,
      );

      if (success) {
        _showSuccessDialog('تم إضافة الخبر بنجاح!');
        _clearNewsForm();
        
        // تحديث قائمة الأخبار
        if (mounted) {
          Provider.of<NewsProvider>(context, listen: false).refreshNews();
        }
      } else {
        _showErrorDialog('حدث خطأ في إضافة الخبر');
      }
    } catch (e) {
      _showErrorDialog('حدث خطأ: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _sendNotification() async {
    if (_notificationTitleController.text.trim().isEmpty ||
        _notificationMessageController.text.trim().isEmpty) {
      _showErrorDialog('يرجى ملء عنوان ورسالة الإشعار');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final success = await SupabaseService.addNotification(
        title: _notificationTitleController.text.trim(),
        message: _notificationMessageController.text.trim(),
        type: _selectedNotificationType,
      );

      if (success) {
        _showSuccessDialog('تم إرسال الإشعار بنجاح!');
        _clearNotificationForm();
        
        // تحديث قائمة الإشعارات
        if (mounted) {
          Provider.of<NotificationProvider>(context, listen: false)
              .refreshNotifications();
        }
      } else {
        _showErrorDialog('حدث خطأ في إرسال الإشعار');
      }
    } catch (e) {
      _showErrorDialog('حدث خطأ: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _clearNewsForm() {
    _newsTitleController.clear();
    _newsContentController.clear();
    _newsImageUrlController.clear();
    _newsAuthorController.clear();
    _newsTagsController.clear();
    setState(() {
      _selectedNewsCategory = 'الدوري المصري';
      _isBreakingNews = false;
    });
  }

  void _clearNotificationForm() {
    _notificationTitleController.clear();
    _notificationMessageController.clear();
    setState(() {
      _selectedNotificationType = 'general';
    });
  }

  void _showSuccessDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('نجح'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('خطأ'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }
}
