import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:provider/provider.dart';
import '../../constants/app_constants.dart';
import '../../widgets/animations/animated_widgets.dart';
import '../../utils/translation_extension.dart';
import '../../providers/language_provider.dart';
import 'more_screen.dart';
import 'favorites_screen.dart';
import 'news_screen.dart';
import 'results_screen.dart';
import '../standings/standings_screen.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> with TickerProviderStateMixin {
  int _currentIndex = 0;
  late PageController _pageController;

  final List<Widget> _screens = [
    const MoreScreen(),
    const FavoritesScreen(),
    const NewsScreen(),
    const StandingsScreen(),
    const ResultsScreen(),
  ];

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: FadeInAnimation(
        duration: const Duration(milliseconds: 800),
        child: const Text(
          AppConstants.appName,
          style: TextStyle(fontWeight: FontWeight.bold, color: Colors.white),
        ),
      ),
      backgroundColor: Theme.of(context).primaryColor,
      elevation: 0,
      // إزالة جميع الأزرار من AppBar - سيتم التعامل معها في صفحة المزيد
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<LanguageProvider>(
      builder: (context, languageProvider, child) {
        return Scaffold(
          appBar: _buildAppBar(),
          body: PageView(
            controller: _pageController,
            onPageChanged: (index) {
              setState(() {
                _currentIndex = index;
              });
            },
            children: _screens,
          ),
          bottomNavigationBar: _buildBottomNavigationBar(),
        );
      },
    );
  }

  Widget _buildBottomNavigationBar() {
    return BottomNavigationBar(
      currentIndex: _currentIndex,
      onTap: (index) {
        setState(() {
          _currentIndex = index;
        });
        _pageController.animateToPage(
          index,
          duration: AppConstants.shortAnimation,
          curve: Curves.easeInOut,
        );
      },
      type: BottomNavigationBarType.fixed,
      selectedFontSize: 10,
      unselectedFontSize: 9,
      items: [
        BottomNavigationBarItem(
          icon: Icon(FontAwesomeIcons.bars, size: 18),
          label: context.tr('more'),
        ),
        BottomNavigationBarItem(
          icon: Icon(FontAwesomeIcons.star, size: 18),
          label: context.tr('favorites'),
        ),
        BottomNavigationBarItem(
          icon: Icon(FontAwesomeIcons.newspaper, size: 18),
          label: context.tr('news'),
        ),
        BottomNavigationBarItem(
          icon: Icon(FontAwesomeIcons.rankingStar, size: 18),
          label: context.tr('league_standings'),
        ),
        BottomNavigationBarItem(
          icon: Icon(FontAwesomeIcons.trophy, size: 18),
          label: context.tr('results'),
        ),
      ],
    );
  }
}
