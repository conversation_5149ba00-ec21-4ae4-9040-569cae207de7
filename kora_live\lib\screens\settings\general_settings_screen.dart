import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:provider/provider.dart';
import '../../providers/theme_provider.dart';
import '../../providers/language_provider.dart';
import '../../providers/font_size_provider.dart';
import '../../utils/translation_extension.dart';

class GeneralSettingsScreen extends StatefulWidget {
  const GeneralSettingsScreen({super.key});

  @override
  State<GeneralSettingsScreen> createState() => _GeneralSettingsScreenState();
}

class _GeneralSettingsScreenState extends State<GeneralSettingsScreen> {
  // إعدادات الإشعارات
  bool _notificationsEnabled = true;
  bool _matchNotifications = true;
  bool _newsNotifications = true;
  bool _favoriteTeamNotifications = true;

  // إعدادات التطبيق
  bool _autoRefresh = true;
  bool _dataCompression = false;
  bool _offlineMode = false;

  // إعدادات العرض - سيتم إدارتها عبر الـ providers

  @override
  Widget build(BuildContext context) {
    return Consumer<LanguageProvider>(
      builder: (context, languageProvider, child) {
        return Scaffold(
          backgroundColor: Theme.of(context).scaffoldBackgroundColor,
          appBar: AppBar(
            title: Text(
              context.tr('general_settings'),
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            backgroundColor: Theme.of(context).primaryColor,
            foregroundColor: Theme.of(context).appBarTheme.foregroundColor,
            elevation: 0,
            leading: IconButton(
              icon: Icon(
                FontAwesomeIcons.arrowRight,
                color: Theme.of(context).appBarTheme.foregroundColor,
              ),
              onPressed: () => Navigator.pop(context),
            ),
          ),
          body: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // قسم المظهر
                _buildSection(
                  title: context.tr('appearance'),
                  icon: FontAwesomeIcons.palette,
                  children: [
                    _buildThemeSelector(),
                    const Divider(height: 1),
                    _buildLanguageSelector(),
                    const Divider(height: 1),
                    _buildFontSizeSlider(),
                  ],
                ),

                const SizedBox(height: 24),

                // قسم الإشعارات
                _buildSection(
                  title: context.tr('notifications'),
                  icon: FontAwesomeIcons.bell,
                  children: [
                    _buildSwitchTile(
                      title: context.tr('enable_notifications'),
                      subtitle: context.tr('receive_app_notifications'),
                      value: _notificationsEnabled,
                      onChanged: (value) {
                        setState(() {
                          _notificationsEnabled = value;
                        });
                      },
                    ),
                    const Divider(height: 1),
                    _buildSwitchTile(
                      title: context.tr('match_notifications'),
                      subtitle: context.tr('match_start_end_alerts'),
                      value: _matchNotifications,
                      onChanged: _notificationsEnabled
                          ? (value) {
                              setState(() {
                                _matchNotifications = value;
                              });
                            }
                          : null,
                    ),
                    const Divider(height: 1),
                    _buildSwitchTile(
                      title: context.tr('news_notifications'),
                      subtitle: context.tr('new_sports_news_alerts'),
                      value: _newsNotifications,
                      onChanged: _notificationsEnabled
                          ? (value) {
                              setState(() {
                                _newsNotifications = value;
                              });
                            }
                          : null,
                    ),
                    const Divider(height: 1),
                    _buildSwitchTile(
                      title: context.tr('favorite_teams_notifications'),
                      subtitle: context.tr('favorite_teams_alerts'),
                      value: _favoriteTeamNotifications,
                      onChanged: _notificationsEnabled
                          ? (value) {
                              setState(() {
                                _favoriteTeamNotifications = value;
                              });
                            }
                          : null,
                    ),
                  ],
                ),

                const SizedBox(height: 24),

                // قسم الأداء والبيانات
                _buildSection(
                  title: context.tr('performance'),
                  icon: FontAwesomeIcons.gauge,
                  children: [
                    _buildSwitchTile(
                      title: context.tr('auto_refresh'),
                      subtitle: context.tr('auto_refresh_description'),
                      value: _autoRefresh,
                      onChanged: (value) {
                        setState(() {
                          _autoRefresh = value;
                        });
                      },
                    ),
                    const Divider(height: 1),
                    _buildSwitchTile(
                      title: context.tr('data_compression'),
                      subtitle: context.tr('save_internet_usage'),
                      value: _dataCompression,
                      onChanged: (value) {
                        setState(() {
                          _dataCompression = value;
                        });
                      },
                    ),
                    const Divider(height: 1),
                    _buildSwitchTile(
                      title: context.tr('offline_mode'),
                      subtitle: context.tr('offline_mode_description'),
                      value: _offlineMode,
                      onChanged: (value) {
                        setState(() {
                          _offlineMode = value;
                        });
                      },
                    ),
                  ],
                ),

                const SizedBox(height: 24),

                // قسم إعادة التعيين
                _buildSection(
                  title: context.tr('reset_section'),
                  icon: FontAwesomeIcons.arrowRotateLeft,
                  children: [
                    _buildActionTile(
                      title: context.tr('reset_settings'),
                      subtitle: context.tr('restore_default_settings'),
                      icon: FontAwesomeIcons.arrowRotateLeft,
                      onTap: _showResetDialog,
                    ),
                    const Divider(height: 1),
                    _buildActionTile(
                      title: context.tr('clear_saved_data'),
                      subtitle: context.tr('delete_all_local_data'),
                      icon: FontAwesomeIcons.trash,
                      onTap: _showClearDataDialog,
                      isDestructive: true,
                    ),
                  ],
                ),

                const SizedBox(height: 32),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSection({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان القسم
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Theme.of(
                      context,
                    ).primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    icon,
                    color: Theme.of(context).primaryColor,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          // محتوى القسم
          ...children,
        ],
      ),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool>? onChanged,
  }) {
    return ListTile(
      title: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          color: onChanged == null
              ? Theme.of(context).textTheme.bodySmall?.color
              : null,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: onChanged == null
              ? Theme.of(
                  context,
                ).textTheme.bodySmall?.color?.withValues(alpha: 0.5)
              : null,
        ),
      ),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
        activeColor: Theme.of(context).primaryColor,
      ),
    );
  }

  Widget _buildActionTile({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: (isDestructive ? Colors.red : Theme.of(context).primaryColor)
              .withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          icon,
          color: isDestructive ? Colors.red : Theme.of(context).primaryColor,
          size: 20,
        ),
      ),
      title: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          color: isDestructive ? Colors.red : null,
        ),
      ),
      subtitle: Text(subtitle, style: Theme.of(context).textTheme.bodySmall),
      trailing: Icon(
        FontAwesomeIcons.chevronLeft,
        size: 16,
        color: Theme.of(context).iconTheme.color,
      ),
      onTap: onTap,
    );
  }

  Widget _buildThemeSelector() {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return ListTile(
          title: Text(
            context.tr('theme'),
            style: Theme.of(context).textTheme.titleMedium,
          ),
          subtitle: Text(
            themeProvider.isDarkMode
                ? context.tr('dark_mode')
                : context.tr('unified_mode'),
            style: Theme.of(context).textTheme.bodySmall,
          ),
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                themeProvider.isDarkMode
                    ? context.tr('dark_mode')
                    : context.tr('unified_mode'),
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).primaryColor,
                ),
              ),
              const SizedBox(width: 8),
              Icon(
                FontAwesomeIcons.chevronLeft,
                size: 16,
                color: Theme.of(context).iconTheme.color,
              ),
            ],
          ),
          onTap: () {
            themeProvider.toggleTheme();
          },
        );
      },
    );
  }

  Widget _buildLanguageSelector() {
    return Consumer<LanguageProvider>(
      builder: (context, languageProvider, child) {
        final currentLanguageName =
            LanguageProvider.languageNames[languageProvider
                .currentLocale
                .languageCode] ??
            'العربية';

        return ListTile(
          title: Text(
            context.tr('language'),
            style: Theme.of(context).textTheme.titleMedium,
          ),
          subtitle: Text(
            currentLanguageName,
            style: Theme.of(context).textTheme.bodySmall,
          ),
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                currentLanguageName,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).primaryColor,
                ),
              ),
              const SizedBox(width: 8),
              Icon(
                FontAwesomeIcons.chevronLeft,
                size: 16,
                color: Theme.of(context).iconTheme.color,
              ),
            ],
          ),
          onTap: _showLanguageDialog,
        );
      },
    );
  }

  Widget _buildFontSizeSlider() {
    return Consumer<FontSizeProvider>(
      builder: (context, fontSizeProvider, child) {
        return ListTile(
          title: Text(
            context.tr('font_size'),
            style: Theme.of(context).textTheme.titleMedium,
          ),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                context.tr('font_size_description'),
                style: Theme.of(context).textTheme.bodySmall,
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Text(
                    context.tr('small'),
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  Expanded(
                    child: Slider(
                      value: fontSizeProvider.fontSize,
                      min: FontSizeProvider.minFontSize,
                      max: FontSizeProvider.maxFontSize,
                      divisions: 6,
                      activeColor: Theme.of(context).primaryColor,
                      onChanged: (value) {
                        fontSizeProvider.setFontSize(value);
                      },
                    ),
                  ),
                  Text(
                    context.tr('large'),
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ),
              Text(
                '${context.tr('preview')}: ${fontSizeProvider.fontSize.toInt()}px',
                style: TextStyle(
                  fontSize: fontSizeProvider.fontSize,
                  color: Theme.of(context).primaryColor,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _showLanguageDialog() {
    showDialog(
      context: context,
      builder: (context) => Consumer<LanguageProvider>(
        builder: (context, languageProvider, child) {
          return AlertDialog(
            backgroundColor: Theme.of(context).cardColor,
            title: Text(
              context.tr('select_language'),
              style: Theme.of(context).textTheme.titleLarge,
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: LanguageProvider.supportedLocales.map((locale) {
                final languageName =
                    LanguageProvider.languageNames[locale.languageCode] ??
                    locale.languageCode;
                return RadioListTile<String>(
                  title: Text(
                    languageName,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  value: locale.languageCode,
                  groupValue: languageProvider.currentLocale.languageCode,
                  activeColor: Theme.of(context).primaryColor,
                  onChanged: (value) {
                    if (value != null) {
                      languageProvider.setLanguage(value);
                    }
                    Navigator.pop(context);
                  },
                );
              }).toList(),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(
                  context.tr('cancel'),
                  style: TextStyle(color: Theme.of(context).primaryColor),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  void _showResetDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Theme.of(context).cardColor,
        title: Text(
          context.tr('reset_confirm_title'),
          style: Theme.of(context).textTheme.titleLarge,
        ),
        content: Text(
          context.tr('reset_confirm_message'),
          style: Theme.of(context).textTheme.bodyMedium,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              context.tr('cancel'),
              style: TextStyle(
                color: Theme.of(context).textTheme.bodyMedium?.color,
              ),
            ),
          ),
          TextButton(
            onPressed: () {
              _resetSettings();
              Navigator.pop(context);
            },
            child: Text(
              context.tr('reset'),
              style: TextStyle(color: Theme.of(context).primaryColor),
            ),
          ),
        ],
      ),
    );
  }

  void _showClearDataDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Theme.of(context).cardColor,
        title: Text(
          context.tr('clear_data_confirm_title'),
          style: Theme.of(
            context,
          ).textTheme.titleLarge?.copyWith(color: Colors.red),
        ),
        content: Text(
          context.tr('clear_data_confirm_message'),
          style: Theme.of(context).textTheme.bodyMedium,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              context.tr('cancel'),
              style: TextStyle(
                color: Theme.of(context).textTheme.bodyMedium?.color,
              ),
            ),
          ),
          TextButton(
            onPressed: () {
              _clearData();
              Navigator.pop(context);
            },
            child: Text(
              context.tr('delete'),
              style: const TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }

  void _resetSettings() {
    setState(() {
      _notificationsEnabled = true;
      _matchNotifications = true;
      _newsNotifications = true;
      _favoriteTeamNotifications = true;
      _autoRefresh = true;
      _dataCompression = false;
      _offlineMode = false;
    });

    // Reset language and font size through providers
    final languageProvider = Provider.of<LanguageProvider>(
      context,
      listen: false,
    );
    final fontSizeProvider = Provider.of<FontSizeProvider>(
      context,
      listen: false,
    );

    languageProvider.setLanguage('ar');
    fontSizeProvider.resetFontSize();

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          context.tr('settings_reset_success'),
          style: Theme.of(
            context,
          ).textTheme.bodyMedium?.copyWith(color: Colors.white),
        ),
        backgroundColor: Theme.of(context).primaryColor,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _clearData() {
    // هنا يمكن إضافة منطق مسح البيانات المحلية
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          context.tr('data_cleared_success'),
          style: Theme.of(
            context,
          ).textTheme.bodyMedium?.copyWith(color: Colors.white),
        ),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
