import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/language_provider.dart';
import 'app_translations.dart';

extension TranslationExtension on BuildContext {
  String tr(String key) {
    try {
      final languageProvider = Provider.of<LanguageProvider>(
        this,
        listen: true,
      );
      return AppTranslations.translate(
        key,
        languageProvider.currentLocale.languageCode,
      );
    } catch (e) {
      // Fallback to Arabic if provider is not available
      return AppTranslations.translate(key, 'ar');
    }
  }

  String get appName => tr('app_name');
  String get home => tr('home');
  String get news => tr('news');
  String get results => tr('results');
  String get favorites => tr('favorites');
  String get more => tr('more');
  String get settings => tr('settings');
  String get cancel => tr('cancel');
  String get confirm => tr('confirm');
  String get save => tr('save');
  String get delete => tr('delete');
  String get reset => tr('reset');
  String get login => tr('login');
  String get logout => tr('logout');
  String get welcome => tr('welcome');
  String get guestWelcome => tr('guest_welcome');
}
