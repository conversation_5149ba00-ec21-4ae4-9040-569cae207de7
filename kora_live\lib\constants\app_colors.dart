import 'package:flutter/material.dart';

class AppColors {
  // Primary Colors - Unified Dark Theme
  static const Color primaryGreen = Color(0xFF141c24); // Main unified color
  static const Color primaryDark = Color(0xFF0f1419); // Darker shade
  static const Color primaryLight = Color(0xFF1f2937); // Lighter shade

  // Secondary Colors - Complementary accents
  static const Color accentOrange = Color(0xFF3b82f6); // Blue accent
  static const Color accentBlue = Color(0xFF60a5fa); // Light blue
  static const Color accentGold = Color(0xFF10b981); // Green accent

  // Background Colors - All unified
  static const Color backgroundLight = Color(0xFF141c24);
  static const Color backgroundDark = Color(0xFF0f1419);
  static const Color cardBackground = Color(0xFF1f2937);
  static const Color cardBackgroundDark = Color(0xFF141c24);

  // White Theme Colors
  static const Color backgroundWhite = Color(0xFFFFFFFF);
  static const Color cardBackgroundWhite = Color(0xFFf8fafc);
  static const Color textPrimaryWhite = Color(0xFF1f2937);
  static const Color textSecondaryWhite = Color(0xFF6b7280);

  // Text Colors - All white for clarity
  static const Color textPrimary = Color(0xFFFFFFFF);
  static const Color textSecondary = Color(0xFFFFFFFF);
  static const Color textLight = Color(0xFFFFFFFF);
  static const Color textMuted = Color(0xFFe2e8f0);

  // Border Colors
  static const Color borderColor = Color(0xFF374151);

  // Status Colors - Adapted for dark theme
  static const Color success = Color(0xFF10b981);
  static const Color warning = Color(0xFFf59e0b);
  static const Color error = Color(0xFFef4444);
  static const Color info = Color(0xFF3b82f6);

  // Gradient Colors - Unified theme
  static const LinearGradient primaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [primaryGreen, primaryLight],
  );

  static const LinearGradient darkGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [primaryDark, primaryGreen],
  );

  static const LinearGradient accentGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [accentOrange, accentBlue],
  );

  // Shadow Colors
  static const Color shadowLight = Color(0x1A000000);
  static const Color shadowMedium = Color(0x33000000);
  static const Color shadowDark = Color(0x4D000000);
}
