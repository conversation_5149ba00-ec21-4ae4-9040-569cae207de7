import "package:flutter/material.dart";
import "package:provider/provider.dart";
import "constants/app_constants.dart";
import "providers/auth_provider.dart";
import "providers/news_provider.dart";
import "providers/notification_provider.dart";
import "providers/theme_provider.dart";
import "providers/language_provider.dart";
import "providers/font_size_provider.dart";

import "screens/splash_screen.dart";
import 'package:supabase_flutter/supabase_flutter.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  await Supabase.initialize(
    url: "https://vgryqmgpuxdbrcxdcovz.supabase.co",
    anonKey:
        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.LouR4IdzLvDsKSLQjvQeq_CwcoxYdEIamvCQ_K1bFW8",
  );
  runApp(const KoraLiveApp());
}

class KoraLiveApp extends StatelessWidget {
  const KoraLiveApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
        ChangeNotifierProvider(create: (_) => NewsProvider()),
        ChangeNotifierProvider(create: (_) => NotificationProvider()),
        ChangeNotifierProvider(create: (_) => ThemeProvider()),
        ChangeNotifierProvider(create: (_) => LanguageProvider()),
        ChangeNotifierProvider(create: (_) => FontSizeProvider()),
      ],
      child: Consumer2<ThemeProvider, LanguageProvider>(
        builder: (context, themeProvider, languageProvider, child) {
          return MaterialApp(
            title: AppConstants.appName,
            debugShowCheckedModeBanner: false,
            theme: themeProvider.currentTheme,
            darkTheme: themeProvider.currentTheme,
            themeMode: ThemeMode
                .light, // Always use light mode to force our custom theme
            // locale: languageProvider.currentLocale,
            // supportedLocales: LanguageProvider.supportedLocales,
            // localizationsDelegates: const [AppLocalizations.delegate],
            home: const SplashScreen(),
            builder: (context, child) {
              return Directionality(
                textDirection: languageProvider.isRTL()
                    ? TextDirection.rtl
                    : TextDirection.ltr,
                child: child!,
              );
            },
          );
        },
      ),
    );
  }
}
