import '../models/news_model.dart';
import '../models/notification_model.dart';

class DummyData {
  static List<NewsModel> getDummyNews() {
    return [
      NewsModel(
        id: '1',
        title: 'ريال مدريد يفوز على برشلونة في الكلاسيكو',
        content: 'حقق ريال مدريد فوزاً مثيراً على برشلونة بنتيجة 2-1 في مباراة الكلاسيكو التي أقيمت على ملعب سانتياغو برنابيو. سجل كريم بنزيما هدفين رائعين في الشوط الثاني ليقود الفريق الملكي للفوز.',
        imageUrl: 'https://images.unsplash.com/photo-1574629810360-7efbbe195018?w=800',
        category: 'الدوري الإسباني',
        publishedAt: DateTime.now().subtract(const Duration(hours: 2)),
        author: 'أحم<PERSON> محمد',
        isBreaking: true,
        readTime: 3,
        tags: ['ريال مدريد', 'برشلونة', 'الكلاسيكو'],
      ),
      NewsModel(
        id: '2',
        title: 'محمد صلاح يسجل هدفين في فوز ليفربول',
        content: 'تألق النجم المصري محمد صلاح وسجل هدفين رائعين قاد بهما ليفربول للفوز على مانشستر يونايتد 3-1 في قمة الدوري الإنجليزي الممتاز.',
        imageUrl: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=800',
        category: 'الدوري الإنجليزي',
        publishedAt: DateTime.now().subtract(const Duration(hours: 5)),
        author: 'سارة أحمد',
        isBreaking: false,
        readTime: 4,
        tags: ['محمد صلاح', 'ليفربول', 'مانشستر يونايتد'],
      ),
      NewsModel(
        id: '3',
        title: 'باريس سان جيرمان يتعاقد مع نجم جديد',
        content: 'أعلن نادي باريس سان جيرمان الفرنسي رسمياً عن التعاقد مع اللاعب البرازيلي الشاب في صفقة قياسية تبلغ قيمتها 80 مليون يورو.',
        imageUrl: 'https://images.unsplash.com/photo-1431324155629-1a6deb1dec8d?w=800',
        category: 'انتقالات',
        publishedAt: DateTime.now().subtract(const Duration(hours: 8)),
        author: 'خالد عبدالله',
        isBreaking: false,
        readTime: 2,
        tags: ['باريس سان جيرمان', 'انتقالات', 'البرازيل'],
      ),
      NewsModel(
        id: '4',
        title: 'منتخب مصر يستعد لكأس الأمم الأفريقية',
        content: 'بدأ منتخب مصر معسكراً تدريبياً مكثفاً استعداداً لبطولة كأس الأمم الأفريقية المقبلة، حيث يسعى الفراعنة لاستعادة اللقب.',
        imageUrl: 'https://images.unsplash.com/photo-1508098682722-e99c43a406b2?w=800',
        category: 'منتخبات',
        publishedAt: DateTime.now().subtract(const Duration(days: 1)),
        author: 'فاطمة علي',
        isBreaking: false,
        readTime: 5,
        tags: ['منتخب مصر', 'كأس الأمم الأفريقية'],
      ),
      NewsModel(
        id: '5',
        title: 'تشيلسي يعين مدرباً جديداً',
        content: 'أعلن نادي تشيلسي الإنجليزي عن تعيين مدرب جديد لقيادة الفريق في الموسم المقبل بعد رحيل المدرب السابق.',
        imageUrl: 'https://images.unsplash.com/photo-1522778119026-d647f0596c20?w=800',
        category: 'الدوري الإنجليزي',
        publishedAt: DateTime.now().subtract(const Duration(days: 2)),
        author: 'محمود حسن',
        isBreaking: false,
        readTime: 3,
        tags: ['تشيلسي', 'مدرب جديد'],
      ),
    ];
  }

  static List<NotificationModel> getDummyNotifications() {
    return [
      NotificationModel(
        id: '1',
        title: 'هدف! ريال مدريد 1-0 برشلونة',
        body: 'سجل كريم بنزيما الهدف الأول لريال مدريد في الدقيقة 25',
        type: NotificationType.goal,
        createdAt: DateTime.now().subtract(const Duration(minutes: 30)),
        isRead: false,
        imageUrl: 'https://images.unsplash.com/photo-1574629810360-7efbbe195018?w=400',
      ),
      NotificationModel(
        id: '2',
        title: 'خبر عاجل: انتقال جديد',
        body: 'باريس سان جيرمان يعلن عن صفقة جديدة بقيمة 80 مليون يورو',
        type: NotificationType.transfer,
        createdAt: DateTime.now().subtract(const Duration(hours: 1)),
        isRead: false,
      ),
      NotificationModel(
        id: '3',
        title: 'بداية المباراة',
        body: 'انطلقت مباراة ليفربول ضد مانشستر يونايتد',
        type: NotificationType.match,
        createdAt: DateTime.now().subtract(const Duration(hours: 2)),
        isRead: true,
      ),
      NotificationModel(
        id: '4',
        title: 'أخبار جديدة',
        body: 'تم نشر مقال جديد عن استعدادات منتخب مصر',
        type: NotificationType.news,
        createdAt: DateTime.now().subtract(const Duration(hours: 4)),
        isRead: true,
      ),
      NotificationModel(
        id: '5',
        title: 'تحديث التطبيق',
        body: 'تم إضافة ميزات جديدة للتطبيق، قم بالتحديث الآن',
        type: NotificationType.general,
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
        isRead: true,
      ),
    ];
  }

  static List<String> getNewsCategories() {
    return [
      'الكل',
      'الدوري الإنجليزي',
      'الدوري الإسباني',
      'الدوري الإيطالي',
      'الدوري الألماني',
      'الدوري الفرنسي',
      'دوري أبطال أوروبا',
      'منتخبات',
      'انتقالات',
    ];
  }

  static List<String> getPopularTags() {
    return [
      'ريال مدريد',
      'برشلونة',
      'ليفربول',
      'مانشستر يونايتد',
      'محمد صلاح',
      'كريستيانو رونالدو',
      'ليونيل ميسي',
      'منتخب مصر',
      'دوري أبطال أوروبا',
      'كأس العالم',
    ];
  }
}
