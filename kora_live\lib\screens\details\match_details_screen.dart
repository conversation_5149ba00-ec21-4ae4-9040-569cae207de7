import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/language_provider.dart';
import 'team_details_screen.dart';

class MatchDetailsScreen extends StatefulWidget {
  final Map<String, dynamic> match;

  const MatchDetailsScreen({super.key, required this.match});

  @override
  State<MatchDetailsScreen> createState() => _MatchDetailsScreenState();
}

class _MatchDetailsScreenState extends State<MatchDetailsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  String _selectedTeam = 'home'; // 'home' أو 'away'
  late AnimationController _formationAnimationController;
  late Animation<double> _playerScaleAnimation; // للأقسام الرئيسية
  late TabController _matchTabController; // للأقسام الفرعية في المباراة
  late TabController _detailsTabController; // للأقسام الفرعية في التفاصيل
  late TabController _statsTabController; // للأقسام الفرعية في الإحصائيات
  bool isFavorite = false;
  bool showHomeTeam = true; // للتبديل بين الفريقين في التشكيلة

  @override
  void initState() {
    super.initState();
    _tabController = TabController(
      length: 3,
      vsync: this,
    ); // الأقسام الرئيسية: المباراة، BUZZ، الملاحظات

    _tabController.addListener(() {
      setState(() {}); // لتحديث الواجهة عند تغيير التبويب
    });

    _matchTabController = TabController(
      length: 3,
      vsync: this,
    ); // الأقسام الفرعية: التفاصيل، التشكيلات، الإحصائيات

    _matchTabController.addListener(() {
      setState(() {}); // لتحديث الواجهة عند تغيير التبويب الفرعي
    });

    _detailsTabController = TabController(
      length: 2,
      vsync: this,
    ); // الأقسام الفرعية في التفاصيل: الأبرز، الكل

    _detailsTabController.addListener(() {
      setState(() {}); // لتحديث الواجهة عند تغيير تبويب التفاصيل
    });

    _statsTabController = TabController(
      length: 3,
      vsync: this,
    ); // الأقسام الفرعية في الإحصائيات: المباراة، الشوط الأول، الشوط الثاني

    _statsTabController.addListener(() {
      setState(() {}); // لتحديث الواجهة عند تغيير تبويب الإحصائيات
    });

    // تهيئة أنيميشن التشكيلة
    _formationAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _playerScaleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _formationAnimationController,
        curve: Curves.elasticOut,
      ),
    );

    // بدء الأنيميشن
    _formationAnimationController.forward();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _matchTabController.dispose();
    _detailsTabController.dispose();
    _statsTabController.dispose();
    _formationAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<LanguageProvider>(
      builder: (context, languageProvider, child) {
        return Scaffold(
          body: CustomScrollView(
            slivers: [
              _buildSliverAppBar(),

              SliverFillRemaining(child: _buildTabBarView()),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSliverAppBar() {
    return SliverAppBar(
      expandedHeight: 160, // قللت الارتفاع من 200 إلى 160
      floating: false,
      pinned: true,
      backgroundColor: const Color(0xFF1A1A1A),
      elevation: 0,
      leading: IconButton(
        icon: const Icon(Icons.arrow_back_ios, color: Colors.white, size: 20),
        onPressed: () => Navigator.of(context).pop(),
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.share, color: Colors.white, size: 24),
          onPressed: () {
            // إضافة وظيفة المشاركة هنا
            ScaffoldMessenger.of(
              context,
            ).showSnackBar(const SnackBar(content: Text('مشاركة المباراة')));
          },
        ),
      ],
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [Color(0xFF2D2D2D), Color(0xFF1A1A1A), Color(0xFF0F0F0F)],
            ),
          ),
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const SizedBox(height: 10), // مساحة صغيرة من الأعلى
                  // صف الشعارات والنتيجة
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // شعار الفريق الأول مع اسم الفريق
                      _buildTeamSection(
                        logo: widget.match['homeLogo'] ?? '🔴',
                        name: widget.match['homeTeam'] ?? 'AC Milan',
                        isHome: true,
                      ),
                      // النتيجة في المنتصف مع تأثيرات
                      _buildScoreSection(),
                      // شعار الفريق الثاني مع اسم الفريق
                      _buildTeamSection(
                        logo: widget.match['awayLogo'] ?? '🔴',
                        name: widget.match['awayTeam'] ?? 'Arsenal',
                        isHome: false,
                      ),
                    ],
                  ),
                  // TabBar في الأسفل مع مساحة صغيرة
                  Padding(
                    padding: const EdgeInsets.only(bottom: 8),
                    child: _buildInlineTabBar(),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTabBarView() {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildMatchTab(), // قسم المباراة (يحتوي على التفاصيل، التشكيلات، الإحصائيات)
        _buildBuzzTab(), // قسم BUZZ
        _buildNotesTab(), // قسم الملاحظات
      ],
    );
  }

  // بناء TabBar مدمج أسفل النتيجة
  Widget _buildInlineTabBar() {
    return Container(
      height: 24,
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.4),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // تبويب المباراة
          Expanded(
            child: GestureDetector(
              onTap: () => _tabController.animateTo(0),
              child: Container(
                decoration: BoxDecoration(
                  gradient: _tabController.index == 0
                      ? LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            const Color(0xFFee0979),
                            const Color(0xFFff6a00),
                          ],
                        )
                      : null,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Center(
                  child: Text(
                    'المباراة',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: _tabController.index == 0
                          ? FontWeight.bold
                          : FontWeight.w500,
                    ),
                  ),
                ),
              ),
            ),
          ),
          // تبويب BUZZ
          Expanded(
            child: GestureDetector(
              onTap: () => _tabController.animateTo(1),
              child: Container(
                decoration: BoxDecoration(
                  gradient: _tabController.index == 1
                      ? LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            const Color(0xFFee0979),
                            const Color(0xFFff6a00),
                          ],
                        )
                      : null,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Center(
                  child: Text(
                    'BUZZ',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: _tabController.index == 1
                          ? FontWeight.bold
                          : FontWeight.w500,
                    ),
                  ),
                ),
              ),
            ),
          ),
          // تبويب الملاحظات
          Expanded(
            child: GestureDetector(
              onTap: () => _tabController.animateTo(2),
              child: Container(
                decoration: BoxDecoration(
                  gradient: _tabController.index == 2
                      ? LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            const Color(0xFFee0979),
                            const Color(0xFFff6a00),
                          ],
                        )
                      : null,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Center(
                  child: Text(
                    'الملاحظات',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: _tabController.index == 2
                          ? FontWeight.bold
                          : FontWeight.w500,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // بناء قسم الفريق مع الشعار والاسم
  Widget _buildTeamSection({
    required String logo,
    required String name,
    required bool isHome,
  }) {
    return GestureDetector(
      onTap: () {
        // الانتقال إلى صفحة تفاصيل الفريق
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => TeamDetailsScreen(
              team: {'name': name, 'logo': logo, 'league': 'الدوري الإسباني'},
            ),
          ),
        );
      },
      child: Column(
        children: [
          // شعار الفريق مع تأثيرات متقدمة
          Container(
            width: 45,
            height: 45,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [Colors.white, Colors.grey[100]!],
              ),
              borderRadius: BorderRadius.circular(22),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.3),
                  spreadRadius: 2,
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
                BoxShadow(
                  color: Colors.white.withValues(alpha: 0.1),
                  spreadRadius: -1,
                  blurRadius: 4,
                  offset: const Offset(0, -2),
                ),
              ],
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            child: Center(
              child: Text(logo, style: const TextStyle(fontSize: 20)),
            ),
          ),
          const SizedBox(height: 2),
          // اسم الفريق
          SizedBox(
            width: 65,
            child: Text(
              name,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 10,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  // بناء قسم النتيجة مع تأثيرات متقدمة
  Widget _buildScoreSection() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xFFee0979).withValues(alpha: 0.8),
            const Color(0xFFff6a00).withValues(alpha: 0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(25),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFFee0979).withValues(alpha: 0.4),
            spreadRadius: 0,
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // نتيجة الفريق الأول
          Text(
            widget.match['homeScore']?.toString() ?? '0',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(width: 8),
          // الفاصل
          Container(
            width: 2,
            height: 20,
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.6),
              borderRadius: BorderRadius.circular(1),
            ),
          ),
          const SizedBox(width: 12),
          // نتيجة الفريق الثاني
          Text(
            widget.match['awayScore']?.toString() ?? '1',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  // قسم المباراة - يحتوي على TabBar فرعي
  Widget _buildMatchTab() {
    return Column(
      children: [
        // TabBar فرعي للتفاصيل والتشكيلات والإحصائيات - تصميم مع خط سفلي
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: TabBar(
            controller: _matchTabController,
            indicator: const UnderlineTabIndicator(
              borderSide: BorderSide(
                color: Color(0xFF007AFF), // لون أزرق مثل الصورة
                width: 3,
              ),
              insets: EdgeInsets.symmetric(horizontal: 20),
            ),
            labelColor: Colors.white,
            unselectedLabelColor: Colors.grey[400],
            labelStyle: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
            unselectedLabelStyle: const TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: 16,
            ),
            indicatorSize: TabBarIndicatorSize.label,
            dividerColor: Colors.transparent,
            tabs: const [
              Tab(text: 'التفاصيل'),
              Tab(text: 'التشكيلات'),
              Tab(text: 'إحصائيات'),
            ],
          ),
        ),

        // محتوى TabBar الفرعي
        Expanded(
          child: TabBarView(
            controller: _matchTabController,
            children: [
              _buildDetailsTab(), // التفاصيل (يحتوي على الأبرز والكل)
              _buildLineupTab(), // التشكيلات
              _buildStatsTab(), // الإحصائيات مع التبويبات الفرعية
            ],
          ),
        ),
      ],
    );
  }

  // قسم BUZZ
  Widget _buildBuzzTab() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: const Center(
        child: Text(
          'قسم BUZZ\nقريباً...',
          textAlign: TextAlign.center,
          style: TextStyle(fontSize: 18, color: Colors.grey),
        ),
      ),
    );
  }

  // قسم الملاحظات
  Widget _buildNotesTab() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: const Center(
        child: Text(
          'قسم الملاحظات\nقريباً...',
          textAlign: TextAlign.center,
          style: TextStyle(fontSize: 18, color: Colors.grey),
        ),
      ),
    );
  }

  // قسم التفاصيل - يحتوي على التبويبات فقط
  Widget _buildDetailsTab() {
    return Column(
      children: [
        // TabBar ثالث للأبرز والكل - تصميم مع خط سفلي
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: TabBar(
            controller: _detailsTabController,
            indicator: const UnderlineTabIndicator(
              borderSide: BorderSide(
                color: Color(0xFF007AFF), // لون أزرق مثل الصورة
                width: 3,
              ),
              insets: EdgeInsets.symmetric(horizontal: 20),
            ),
            labelColor: Colors.white,
            unselectedLabelColor: Colors.grey[400],
            labelStyle: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
            unselectedLabelStyle: const TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: 16,
            ),
            indicatorSize: TabBarIndicatorSize.label,
            dividerColor: Colors.transparent,
            tabs: const [
              Tab(text: 'الأبرز'),
              Tab(text: 'الكل'),
            ],
          ),
        ),
        // محتوى TabBar الثالث
        Expanded(
          child: TabBarView(
            controller: _detailsTabController,
            children: [
              _buildHighlightsTab(), // الأبرز
              _buildAllEventsTab(), // الكل (Timeline)
            ],
          ),
        ),
      ],
    );
  }

  // قسم وقت اللعب الفعلي
  Widget _buildMatchTimeSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[700]!),
      ),
      child: Column(
        children: [
          // عنوان القسم
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.access_time, color: Colors.blue, size: 20),
              const SizedBox(width: 8),
              Text(
                'وقت اللعب الفعلي',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // شريط التقدم والأوقات
          Column(
            children: [
              // الوقت الحالي
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: Colors.blue,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  'لعب بالفعل 54:07',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const SizedBox(height: 12),

              // شريط التقدم
              Container(
                height: 8,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4),
                  color: Colors.grey[700],
                ),
                child: FractionallySizedBox(
                  alignment: Alignment.centerLeft,
                  widthFactor: 0.6, // 60% من الوقت
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(4),
                      color: Colors.blue,
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 12),

              // الوقت الإجمالي
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: Colors.grey[800],
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  'الوقت الإجمالي 100:34',
                  style: TextStyle(color: Colors.white, fontSize: 14),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // إظهار المزيد
          Text(
            'إظهار المزيد',
            style: TextStyle(color: Colors.grey[400], fontSize: 14),
          ),
        ],
      ),
    );
  }

  // قسم أبرز الإحصائيات
  Widget _buildHighlightStatsSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[700]!),
      ),
      child: Column(
        children: [
          // عنوان القسم
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.bar_chart, color: Colors.blue, size: 20),
              const SizedBox(width: 8),
              Text(
                'أبرز الإحصائيات',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),

          // الإحصائيات
          _buildHighlightStatRow(
            'الاستحواذ',
            '52%',
            '48%',
            Colors.red,
            Colors.grey[600]!,
          ),
          _buildHighlightStatRow(
            'أهداف متوقعة',
            '0.63',
            '1.84',
            Colors.grey[600]!,
            Colors.blue,
          ),
          _buildHighlightStatRow(
            'إجمالي التسديدات',
            '13',
            '8',
            Colors.red,
            Colors.grey[600]!,
          ),
          _buildHighlightStatRow(
            'تسديدات على المرمى',
            '2',
            '3',
            Colors.grey[600]!,
            Colors.blue,
          ),
          _buildHighlightStatRow(
            'فرص خطيرة',
            '0',
            '4',
            Colors.grey[600]!,
            Colors.blue,
          ),
          _buildHighlightStatRow(
            'الضربات الركنية',
            '3',
            '3',
            Colors.grey[600]!,
            Colors.grey[600]!,
          ),
          _buildHighlightStatRow(
            'حالات التسلل',
            '3',
            '4',
            Colors.red,
            Colors.grey[600]!,
          ),
          _buildHighlightStatRow(
            'تمريرات صحيحة',
            '407',
            '374',
            Colors.red,
            Colors.grey[600]!,
          ),
          _buildHighlightStatRow(
            'البطاقات الحمراء',
            '0',
            '0',
            Colors.grey[600]!,
            Colors.grey[600]!,
          ),
          _buildHighlightStatRow(
            'هجمات',
            '79',
            '71',
            Colors.red,
            Colors.grey[600]!,
          ),
        ],
      ),
    );
  }

  // بناء صف إحصائية واحدة
  Widget _buildHighlightStatRow(
    String label,
    String homeValue,
    String awayValue,
    Color homeColor,
    Color awayColor,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          // القيمة اليسرى (الفريق الأول)
          Container(
            width: 40,
            height: 30,
            decoration: BoxDecoration(
              color: homeColor,
              borderRadius: BorderRadius.circular(15),
            ),
            child: Center(
              child: Text(
                homeValue,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),

          // المسافة
          const SizedBox(width: 16),

          // التسمية
          Expanded(
            child: Text(
              label,
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.white, fontSize: 14),
            ),
          ),

          // المسافة
          const SizedBox(width: 16),

          // القيمة اليمنى (الفريق الثاني)
          Container(
            width: 40,
            height: 30,
            decoration: BoxDecoration(
              color: awayColor,
              borderRadius: BorderRadius.circular(15),
            ),
            child: Center(
              child: Text(
                awayValue,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // قسم الإحصائيات مع التبويبات الفرعية
  Widget _buildStatsTab() {
    return Column(
      children: [
        // تبويبات الإحصائيات الفرعية
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: TabBar(
            controller: _statsTabController,
            indicator: const UnderlineTabIndicator(
              borderSide: BorderSide(
                color: Color(0xFF007AFF), // خط أزرق
                width: 3,
              ),
              insets: EdgeInsets.symmetric(horizontal: 20),
            ),
            labelColor: Colors.white,
            unselectedLabelColor: Colors.white.withValues(alpha: 0.7),
            labelStyle: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
            unselectedLabelStyle: const TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: 16,
            ),
            indicatorSize: TabBarIndicatorSize.label,
            dividerColor: Colors.transparent,
            tabs: const [
              Tab(text: 'المباراة'),
              Tab(text: 'الشوط الأول'),
              Tab(text: 'الشوط الثاني'),
            ],
          ),
        ),
        // محتوى التبويبات الفرعية
        Expanded(
          child: TabBarView(
            controller: _statsTabController,
            children: [
              _buildMatchStatsWithDetails(), // المباراة مع وقت اللعب وأبرز الإحصائيات
              _buildFirstHalfStatsTab(), // الشوط الأول فقط
              _buildSecondHalfStatsTab(), // الشوط الثاني فقط
            ],
          ),
        ),
      ],
    );
  }

  // محتوى المباراة مع وقت اللعب وأبرز الإحصائيات
  Widget _buildMatchStatsWithDetails() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // وقت اللعب الفعلي
          _buildMatchTimeSection(),
          const SizedBox(height: 16),

          // أبرز الإحصائيات
          _buildHighlightStatsSection(),
        ],
      ),
    );
  }

  // قسم الأبرز - الأحداث المهمة فقط
  Widget _buildHighlightsTab() {
    // أحداث المباراة الحية - الأبرز فقط
    final highlights = [
      {
        'time': '90+5\'',
        'type': 'goal',
        'player': 'ماركوس ليتانادرو',
        'playerAr': 'ماركوس ليتانادرو',
        'team': 'home',
        'description': 'روبين تنفيذ',
      },
      {
        'time': '86\'',
        'type': 'substitution',
        'player': 'مصعب الجعيدر',
        'playerAr': 'مصعب الجعيدر',
        'team': 'away',
        'description': 'مالكوم دي أوليفيرا',
      },
      {
        'time': '86\'',
        'type': 'substitution',
        'player': 'حمد الباسي',
        'playerAr': 'حمد الباسي',
        'team': 'away',
        'description': 'جوار كاسيليو',
      },
      {
        'time': '77\'',
        'type': 'substitution',
        'player': 'علي لاجامي',
        'playerAr': 'علي لاجامي',
        'team': 'away',
        'description': 'حسان تميمي',
      },
    ];

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            const Color(0xFF1a1a2e),
            const Color(0xFF16213e),
            const Color(0xFF0f3460),
          ],
        ),
      ),
      child: SingleChildScrollView(
        child: Column(
          children: [
            // عنوان المباراة الحية
            Container(
              margin: const EdgeInsets.all(12),
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(25),
                border: Border.all(color: Colors.white.withValues(alpha: 0.2)),
              ),
              child: Text(
                'أحداث المباراة الحية',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ),

            // Timeline الأحداث الحية
            _buildLiveTimeline(highlights),
          ],
        ),
      ),
    );
  }

  // بناء Timeline الأحداث الحية مثل الصورة
  Widget _buildLiveTimeline(List<Map<String, dynamic>> events) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          // خط Timeline العمودي مع الأحداث
          for (int i = 0; i < events.length; i++)
            _buildTimelineItem(
              events[i],
              i == 0, // isFirst
              i == events.length - 1, // isLast
            ),
        ],
      ),
    );
  }

  // بناء عنصر واحد في Timeline
  Widget _buildTimelineItem(
    Map<String, dynamic> event,
    bool isFirst,
    bool isLast,
  ) {
    final time = event['time'] as String;
    final type = event['type'] as String;
    final player = event['player'] as String;
    final team = event['team'] as String;
    final description = event['description'] as String? ?? '';

    // تحديد لون ونوع الأيقونة
    IconData icon;
    Color iconColor;

    switch (type) {
      case 'goal':
        icon = Icons.sports_soccer;
        iconColor = Colors.white;
        break;
      case 'substitution':
        icon = Icons.swap_horiz;
        iconColor = Colors.green;
        break;
      case 'yellow_card':
        icon = Icons.crop_square;
        iconColor = Colors.yellow;
        break;
      case 'red_card':
        icon = Icons.crop_square;
        iconColor = Colors.red;
        break;
      default:
        icon = Icons.circle;
        iconColor = Colors.white;
    }

    return Container(
      height: 60,
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          // الجانب الأيسر - أحداث الفريق المضيف
          Expanded(
            flex: 5,
            child: team == 'home'
                ? _buildEventDetails(player, description, time, true)
                : const SizedBox(), // فارغ إذا لم يكن الفريق المضيف
          ),

          // الوسط - الخط العمودي والأيقونة
          SizedBox(
            width: 60,
            child: Column(
              children: [
                // الخط العلوي
                if (!isFirst)
                  Container(
                    width: 2,
                    height: 15,
                    color: Colors.white.withValues(alpha: 0.3),
                  ),

                // الأيقونة
                Container(
                  width: 30,
                  height: 30,
                  decoration: BoxDecoration(
                    color: iconColor,
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: Colors.white.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Icon(
                    icon,
                    color: type == 'yellow_card' || type == 'red_card'
                        ? Colors.black
                        : Colors.black,
                    size: 16,
                  ),
                ),

                // الخط السفلي
                if (!isLast)
                  Container(
                    width: 2,
                    height: 15,
                    color: Colors.white.withValues(alpha: 0.3),
                  ),
              ],
            ),
          ),

          // الجانب الأيمن - أحداث الفريق الضيف
          Expanded(
            flex: 5,
            child: team == 'away'
                ? _buildEventDetails(player, description, time, false)
                : const SizedBox(), // فارغ إذا لم يكن الفريق الضيف
          ),
        ],
      ),
    );
  }

  // قسم الكل - Timeline كامل للأحداث مثل الصورة
  Widget _buildAllEventsTab() {
    // جميع أحداث المباراة مثل الصورة
    final allEvents = [
      // الشوط الثاني
      {
        'time': '90+5\'',
        'type': 'goal',
        'player': 'ماركوس ليتانادرو',
        'team': 'home',
        'description': 'روبين تنفيذ',
      },
      {
        'time': '86\'',
        'type': 'substitution',
        'player': 'مصعب الجعيدر',
        'team': 'away',
        'description': 'مالكوم دي أوليفيرا',
      },
      {
        'time': '86\'',
        'type': 'substitution',
        'player': 'حمد الباسي',
        'team': 'away',
        'description': 'جوار كاسيليو',
      },
      {
        'time': '77\'',
        'type': 'substitution',
        'player': 'علي لاجامي',
        'team': 'away',
        'description': 'حسان تميمي',
        'playerOut': 'إيلان هيرنانديز',
        'playerOutAr': 'كارلوس سانشيز',
      },
      {
        'time': '77\'',
        'type': 'substitution',
        'player': 'بيدرو بيدارا',
        'team': 'home',
        'description': 'أوغستين بلاكسينو',
      },
      {
        'time': '76\'',
        'type': 'substitution',
        'player': 'محمد كعب',
        'team': 'away',
        'description': 'سالم الدوسري',
      },
      {
        'time': '71\'',
        'type': 'substitution',
        'player': 'منذر الحربي',
        'team': 'away',
        'description': 'ريان إيدي',
      },
      {
        'time': '63\'',
        'type': 'substitution',
        'player': 'آلان بابيستا',
        'team': 'home',
        'description': 'كيبيدي',
      },
      {
        'time': '57\'',
        'type': 'substitution',
        'player': 'بريان غيرا ليس',
        'team': 'home',
        'description': 'سيرجيو باريتو',
      },
      {
        'time': '57\'',
        'type': 'substitution',
        'player': 'أفيليس هورنادو',
        'team': 'home',
        'description': 'ألكسي دومينغيز',
      },
      {
        'time': '52\'',
        'type': 'yellow_card',
        'player': 'خاليد كليباني',
        'team': 'away',
      },
      // الشوط الأول
      {
        'time': '45+4\'',
        'type': 'yellow_card',
        'player': 'ريان إيدي',
        'team': 'away',
      },
      {
        'time': '45+2\'',
        'type': 'yellow_card',
        'player': 'ألكسي دومينغيز',
        'team': 'home',
      },
      {
        'time': '22\'',
        'type': 'goal',
        'player': 'سالم الدوسري',
        'team': 'away',
        'description': 'ناصر الدوسري',
      },
      {
        'time': '17\'',
        'type': 'yellow_card',
        'player': 'سيرجيو باريتو',
        'team': 'home',
      },
    ];

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            const Color(0xFF1a1a2e),
            const Color(0xFF16213e),
            const Color(0xFF0f3460),
          ],
        ),
      ),
      child: SingleChildScrollView(
        child: Column(
          children: [
            // عنوان الشوط الثاني
            Container(
              margin: const EdgeInsets.all(12),
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.4),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(color: Colors.white.withValues(alpha: 0.2)),
              ),
              child: Text(
                'نهاية الـ 90 دقيقة 0 - 2',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ),

            // عناوين الفرق والوقت
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Row(
                children: [
                  Expanded(
                    flex: 5,
                    child: Text(
                      'الفريق المضيف',
                      style: TextStyle(
                        color: Colors.green.withValues(alpha: 0.8),
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  SizedBox(
                    width: 60,
                    child: Text(
                      'الوقت',
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.6),
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Expanded(
                    flex: 5,
                    child: Text(
                      'الفريق الضيف',
                      style: TextStyle(
                        color: Colors.red.withValues(alpha: 0.8),
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),
            ),

            _buildFullTimeline(allEvents),

            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  // بناء Timeline كامل مثل الصورة
  Widget _buildFullTimeline(List<Map<String, dynamic>> events) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          // خط Timeline العمودي مع الأحداث
          for (int i = 0; i < events.length; i++)
            _buildFullTimelineItem(
              events[i],
              i == 0, // isFirst
              i == events.length - 1, // isLast
            ),
        ],
      ),
    );
  }

  // بناء عنصر واحد في Timeline مثل الصورة - خط في الوسط
  Widget _buildFullTimelineItem(
    Map<String, dynamic> event,
    bool isFirst,
    bool isLast,
  ) {
    final time = event['time'] as String;
    final type = event['type'] as String;
    final player = event['player'] as String;
    final team = event['team'] as String;
    final description = event['description'] as String? ?? '';

    // تحديد لون ونوع الأيقونة
    IconData icon;
    Color iconColor;

    switch (type) {
      case 'goal':
        icon = Icons.sports_soccer;
        iconColor = Colors.white;
        break;
      case 'substitution':
        icon = Icons.swap_horiz;
        iconColor = Colors.green;
        break;
      case 'yellow_card':
        icon = Icons.crop_square;
        iconColor = Colors.yellow;
        break;
      case 'red_card':
        icon = Icons.crop_square;
        iconColor = Colors.red;
        break;
      default:
        icon = Icons.circle;
        iconColor = Colors.white;
    }

    return Container(
      height: 60,
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          // الجانب الأيسر - أحداث الفريق المضيف
          Expanded(
            flex: 5,
            child: team == 'home'
                ? _buildEventDetails(player, description, time, true)
                : const SizedBox(), // فارغ إذا لم يكن الفريق المضيف
          ),

          // الوسط - الخط العمودي والأيقونة
          SizedBox(
            width: 60,
            child: Column(
              children: [
                // الخط العلوي
                if (!isFirst)
                  Container(
                    width: 2,
                    height: 15,
                    color: Colors.white.withValues(alpha: 0.3),
                  ),

                // الأيقونة مع الوقت
                Container(
                  width: 30,
                  height: 30,
                  decoration: BoxDecoration(
                    color: iconColor,
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: Colors.white.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Icon(
                    icon,
                    color: type == 'yellow_card' || type == 'red_card'
                        ? Colors.black
                        : Colors.black,
                    size: 16,
                  ),
                ),

                // الخط السفلي
                if (!isLast)
                  Container(
                    width: 2,
                    height: 15,
                    color: Colors.white.withValues(alpha: 0.3),
                  ),
              ],
            ),
          ),

          // الجانب الأيمن - أحداث الفريق الضيف
          Expanded(
            flex: 5,
            child: team == 'away'
                ? _buildEventDetails(player, description, time, false)
                : const SizedBox(), // فارغ إذا لم يكن الفريق الضيف
          ),
        ],
      ),
    );
  }

  // بناء تفاصيل الحدث للفريق
  Widget _buildEventDetails(
    String player,
    String description,
    String time,
    bool isHome,
  ) {
    return Container(
      padding: EdgeInsets.only(left: isHome ? 0 : 8, right: isHome ? 8 : 0),
      child: Column(
        crossAxisAlignment: isHome
            ? CrossAxisAlignment.end
            : CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // اسم اللاعب
          Text(
            player,
            style: TextStyle(
              color: isHome ? Colors.green : Colors.red,
              fontSize: 13,
              fontWeight: FontWeight.bold,
            ),
            textAlign: isHome ? TextAlign.right : TextAlign.left,
          ),

          // الوصف (إذا وجد)
          if (description.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(top: 2),
              child: Text(
                description,
                style: TextStyle(
                  color: Colors.white.withValues(alpha: 0.7),
                  fontSize: 11,
                ),
                textAlign: isHome ? TextAlign.right : TextAlign.left,
              ),
            ),

          // الوقت
          Text(
            time,
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.6),
              fontSize: 10,
              fontWeight: FontWeight.bold,
            ),
            textAlign: isHome ? TextAlign.right : TextAlign.left,
          ),
        ],
      ),
    );
  }

  // إحصائيات الشوط الأول
  Widget _buildFirstHalfStatsTab() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Center(
        child: Text(
          'إحصائيات الشوط الأول',
          style: TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  // إحصائيات الشوط الثاني
  Widget _buildSecondHalfStatsTab() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Center(
        child: Text(
          'إحصائيات الشوط الثاني',
          style: TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  Widget _buildLineupTab() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            const Color(0xFF1a1a2e),
            const Color(0xFF16213e),
            const Color(0xFF0f3460),
          ],
        ),
      ),
      child: Column(
        children: [
          // تبويبات الفرق مطابقة للصورة
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
            height: 60,
            decoration: BoxDecoration(
              color: const Color(0xFF2A3441), // خلفية رمادية داكنة
              borderRadius: BorderRadius.circular(30),
            ),
            child: Row(
              children: [
                // تبويب الفريق الأول (الهلال)
                Expanded(
                  child: GestureDetector(
                    onTap: () {
                      setState(() {
                        _selectedTeam = 'home';
                      });
                      _formationAnimationController.reset();
                      _formationAnimationController.forward();
                    },
                    child: AnimatedContainer(
                      duration: const Duration(milliseconds: 300),
                      margin: const EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        color: _selectedTeam == 'home'
                            ? const Color(0xFF007AFF) // أزرق للمختار
                            : Colors.transparent,
                        borderRadius: BorderRadius.circular(26),
                      ),
                      child: Center(
                        child: Text(
                          'الهلال',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),

                // تبويب الفريق الثاني (باتشوكا)
                Expanded(
                  child: GestureDetector(
                    onTap: () {
                      setState(() {
                        _selectedTeam = 'away';
                      });
                      _formationAnimationController.reset();
                      _formationAnimationController.forward();
                    },
                    child: AnimatedContainer(
                      duration: const Duration(milliseconds: 300),
                      margin: const EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        color: _selectedTeam == 'away'
                            ? const Color(0xFF007AFF) // أزرق للمختار
                            : Colors.transparent,
                        borderRadius: BorderRadius.circular(26),
                      ),
                      child: Center(
                        child: Text(
                          'باتشوكا',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // ملعب كرة القدم مع التشكيل
          Expanded(
            child: Container(
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    const Color(0xFF2E7D32), // أخضر غامق
                    const Color(0xFF4CAF50), // أخضر متوسط
                    const Color(0xFF66BB6A), // أخضر فاتح
                    const Color(0xFF4CAF50), // أخضر متوسط
                    const Color(0xFF2E7D32), // أخضر غامق
                  ],
                ),
              ),
              child: Stack(
                children: [
                  // خطوط الملعب
                  SizedBox(
                    width: double.infinity,
                    height: double.infinity,
                    child: CustomPaint(painter: SimpleFieldPainter()),
                  ),

                  // اللاعبين
                  _buildSimpleFormation(),

                  // رقم التشكيل
                  Positioned(
                    bottom: 16,
                    right: 16,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.6),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '3-4-3',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // بناء تشكيلة كرة القدم الاحترافية
  Widget _buildSimpleFormation() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            const Color(0xFF2E7D32), // أخضر غامق
            const Color(0xFF4CAF50), // أخضر متوسط
            const Color(0xFF66BB6A), // أخضر فاتح
            const Color(0xFF4CAF50), // أخضر متوسط
            const Color(0xFF2E7D32), // أخضر غامق
          ],
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(20),
        child: Stack(
          children: [
            // خطوط الملعب
            CustomPaint(painter: FootballFieldPainter(), size: Size.infinite),

            // التشكيلة الاحترافية
            _buildProfessionalFormation(),

            // معلومات التشكيل
            Positioned(
              bottom: 16,
              right: 16,
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.7),
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Text(
                  _selectedTeam == 'home' ? '3-4-3 الهلال' : '4-3-3 باتشوكا',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // بناء التشكيلة الاحترافية
  Widget _buildProfessionalFormation() {
    final players = _selectedTeam == 'home'
        ? _getHomePlayersData()
        : _getAwayPlayersData();

    return LayoutBuilder(
      builder: (context, constraints) {
        final width = constraints.maxWidth;
        final height = constraints.maxHeight;

        return Stack(
          children: players.map((player) {
            return Positioned(
              left: player['x'] * width - 35,
              top: player['y'] * height - 35,
              child: AnimatedScale(
                scale: _playerScaleAnimation.value,
                duration: const Duration(milliseconds: 300),
                child: _buildPlayerCard(player),
              ),
            );
          }).toList(),
        );
      },
    );
  }

  // بناء كارت اللاعب الاحترافي
  Widget _buildPlayerCard(Map<String, dynamic> player) {
    final isCaptain = player['isCaptain'] ?? false;

    return Container(
      width: 70,
      height: 90,
      child: Column(
        children: [
          // صورة اللاعب مع التقييم والرقم
          Stack(
            children: [
              // صورة اللاعب
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: isCaptain ? Colors.yellow : Colors.white,
                    width: 2,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.3),
                      blurRadius: 4,
                      offset: Offset(0, 2),
                    ),
                  ],
                ),
                child: ClipOval(
                  child: player['image'] != null
                      ? Image.network(
                          player['image'],
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return _buildDefaultPlayerAvatar(player['name']);
                          },
                        )
                      : _buildDefaultPlayerAvatar(player['name']),
                ),
              ),

              // رقم القميص
              Positioned(
                top: -2,
                right: -2,
                child: Container(
                  width: 18,
                  height: 18,
                  decoration: BoxDecoration(
                    color: _selectedTeam == 'home' ? Colors.blue : Colors.red,
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.white, width: 1),
                  ),
                  child: Center(
                    child: Text(
                      player['number'].toString(),
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ),

              // علامة الكابتن
              if (isCaptain)
                Positioned(
                  bottom: -2,
                  left: -2,
                  child: Container(
                    width: 16,
                    height: 16,
                    decoration: BoxDecoration(
                      color: Colors.yellow,
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.black, width: 1),
                    ),
                    child: Center(
                      child: Text(
                        'C',
                        style: TextStyle(
                          color: Colors.black,
                          fontSize: 8,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ),

              // التقييم
              Positioned(
                bottom: -5,
                right: -5,
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                  decoration: BoxDecoration(
                    color: _getRatingColor(player['rating']),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.white, width: 1),
                  ),
                  child: Text(
                    player['rating'].toString(),
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 8,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ),

          SizedBox(height: 4),

          // اسم اللاعب
          Container(
            padding: EdgeInsets.symmetric(horizontal: 4, vertical: 2),
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.7),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              player['name'],
              style: TextStyle(
                color: Colors.white,
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  // بناء صورة افتراضية للاعب
  Widget _buildDefaultPlayerAvatar(String name) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.blue.shade400, Colors.blue.shade600],
        ),
      ),
      child: Center(
        child: Text(
          name.isNotEmpty ? name[0].toUpperCase() : '?',
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  // تحديد لون التقييم
  Color _getRatingColor(double rating) {
    if (rating >= 8.0) return Colors.green;
    if (rating >= 7.0) return Colors.orange;
    if (rating >= 6.0) return Colors.yellow.shade700;
    return Colors.red;
  }

  // بيانات لاعبي الهلال (تشكيلة 3-4-3)
  List<Map<String, dynamic>> _getHomePlayersData() {
    return [
      // حارس المرمى
      {
        'name': 'بونو',
        'number': 1,
        'rating': 7.8,
        'x': 0.5,
        'y': 0.9,
        'isCaptain': false,
        'image': null,
      },

      // خط الدفاع (3 لاعبين)
      {
        'name': 'كوليبالي',
        'number': 3,
        'rating': 7.5,
        'x': 0.25,
        'y': 0.75,
        'isCaptain': false,
        'image': null,
      },
      {
        'name': 'الشهراني',
        'number': 12,
        'rating': 7.2,
        'x': 0.5,
        'y': 0.75,
        'isCaptain': true, // الكابتن
        'image': null,
      },
      {
        'name': 'البولايحي',
        'number': 5,
        'rating': 7.0,
        'x': 0.75,
        'y': 0.75,
        'isCaptain': false,
        'image': null,
      },

      // خط الوسط (4 لاعبين)
      {
        'name': 'كانتي',
        'number': 7,
        'rating': 8.2,
        'x': 0.15,
        'y': 0.55,
        'isCaptain': false,
        'image': null,
      },
      {
        'name': 'ميلينكوفيتش',
        'number': 22,
        'rating': 7.8,
        'x': 0.4,
        'y': 0.55,
        'isCaptain': false,
        'image': null,
      },
      {
        'name': 'الدوسري',
        'number': 29,
        'rating': 7.4,
        'x': 0.6,
        'y': 0.55,
        'isCaptain': false,
        'image': null,
      },
      {
        'name': 'الحمدان',
        'number': 28,
        'rating': 7.1,
        'x': 0.85,
        'y': 0.55,
        'isCaptain': false,
        'image': null,
      },

      // خط الهجوم (3 لاعبين)
      {
        'name': 'نيمار',
        'number': 10,
        'rating': 8.5,
        'x': 0.25,
        'y': 0.3,
        'isCaptain': false,
        'image': null,
      },
      {
        'name': 'ميتروفيتش',
        'number': 9,
        'rating': 8.0,
        'x': 0.5,
        'y': 0.25,
        'isCaptain': false,
        'image': null,
      },
      {
        'name': 'مالكوم',
        'number': 77,
        'rating': 7.6,
        'x': 0.75,
        'y': 0.3,
        'isCaptain': false,
        'image': null,
      },
    ];
  }

  // بيانات لاعبي باتشوكا (تشكيلة 4-3-3)
  List<Map<String, dynamic>> _getAwayPlayersData() {
    return [
      // حارس المرمى
      {
        'name': 'مورينو',
        'number': 1,
        'rating': 7.2,
        'x': 0.5,
        'y': 0.9,
        'isCaptain': false,
        'image': null,
      },

      // خط الدفاع (4 لاعبين)
      {
        'name': 'رودريجيز',
        'number': 2,
        'rating': 6.8,
        'x': 0.15,
        'y': 0.75,
        'isCaptain': false,
        'image': null,
      },
      {
        'name': 'باريديس',
        'number': 4,
        'rating': 7.0,
        'x': 0.35,
        'y': 0.75,
        'isCaptain': true, // الكابتن
        'image': null,
      },
      {
        'name': 'كابرال',
        'number': 6,
        'rating': 6.9,
        'x': 0.65,
        'y': 0.75,
        'isCaptain': false,
        'image': null,
      },
      {
        'name': 'غونزاليز',
        'number': 3,
        'rating': 6.7,
        'x': 0.85,
        'y': 0.75,
        'isCaptain': false,
        'image': null,
      },

      // خط الوسط (3 لاعبين)
      {
        'name': 'مونتيل',
        'number': 8,
        'rating': 7.3,
        'x': 0.3,
        'y': 0.55,
        'isCaptain': false,
        'image': null,
      },
      {
        'name': 'ألفاريز',
        'number': 5,
        'rating': 7.1,
        'x': 0.5,
        'y': 0.55,
        'isCaptain': false,
        'image': null,
      },
      {
        'name': 'بيريز',
        'number': 16,
        'rating': 6.9,
        'x': 0.7,
        'y': 0.55,
        'isCaptain': false,
        'image': null,
      },

      // خط الهجوم (3 لاعبين)
      {
        'name': 'إيدسون',
        'number': 9,
        'rating': 7.8,
        'x': 0.25,
        'y': 0.3,
        'isCaptain': false,
        'image': null,
      },
      {
        'name': 'رونالدينيو',
        'number': 10,
        'rating': 8.1,
        'x': 0.5,
        'y': 0.25,
        'isCaptain': false,
        'image': null,
      },
      {
        'name': 'سالينو',
        'number': 7,
        'rating': 7.4,
        'x': 0.75,
        'y': 0.3,
        'isCaptain': false,
        'image': null,
      },
    ];
  }

  // الحصول على لاعبي العراق بطريقة بسيطة
  List<Map<String, dynamic>> _getHomePlayersSimple() {
    return [
      // حارس المرمى
      {'name': 'جلال', 'number': '1', 'rating': '7.2', 'x': 0.5, 'y': 0.9},

      // خط الدفاع (4 لاعبين)
      {'name': 'أحمد', 'number': '2', 'rating': '6.8', 'x': 0.15, 'y': 0.7},
      {'name': 'ريبين', 'number': '4', 'rating': '7.1', 'x': 0.4, 'y': 0.7},
      {'name': 'فرانز', 'number': '5', 'rating': '6.9', 'x': 0.6, 'y': 0.7},
      {'name': 'مصطفى', 'number': '3', 'rating': '6.6', 'x': 0.85, 'y': 0.7},

      // خط الوسط (4 لاعبين)
      {'name': 'أمجد', 'number': '8', 'rating': '7.3', 'x': 0.15, 'y': 0.5},
      {'name': 'إبراهيم', 'number': '6', 'rating': '6.7', 'x': 0.4, 'y': 0.5},
      {'name': 'علي', 'number': '23', 'rating': '6.4', 'x': 0.6, 'y': 0.5},
      {'name': 'أيمن', 'number': '9', 'rating': '7.0', 'x': 0.85, 'y': 0.5},

      // خط الهجوم (2 لاعبين)
      {'name': 'عيسى', 'number': '10', 'rating': '7.5', 'x': 0.35, 'y': 0.3},
      {'name': 'مهند', 'number': '11', 'rating': '6.8', 'x': 0.65, 'y': 0.3},
    ];
  }

  // الحصول على لاعبي الأردن بطريقة بسيطة
  List<Map<String, dynamic>> _getAwayPlayersSimple() {
    return [
      // حارس المرمى
      {'name': 'أبو ليلى', 'number': '1', 'rating': '6.5', 'x': 0.5, 'y': 0.9},

      // خط الدفاع (3 لاعبين)
      {'name': 'نصيف', 'number': '3', 'rating': '6.7', 'x': 0.25, 'y': 0.7},
      {'name': 'العربي', 'number': '5', 'rating': '6.7', 'x': 0.5, 'y': 0.7},
      {'name': 'عبيد', 'number': '16', 'rating': '6.2', 'x': 0.75, 'y': 0.7},

      // خط الوسط (4 لاعبين)
      {'name': 'أبو طه', 'number': '20', 'rating': '6.1', 'x': 0.15, 'y': 0.5},
      {'name': 'سعادة', 'number': '15', 'rating': '6.1', 'x': 0.4, 'y': 0.5},
      {'name': 'الرشدان', 'number': '21', 'rating': '6.5', 'x': 0.6, 'y': 0.5},
      {'name': 'عساف', 'number': '17', 'rating': '6.9', 'x': 0.85, 'y': 0.5},

      // خط الهجوم (3 لاعبين)
      {'name': 'المرضي', 'number': '13', 'rating': '6.7', 'x': 0.25, 'y': 0.3},
      {'name': 'التعبان', 'number': '11', 'rating': '6.5', 'x': 0.5, 'y': 0.3},
      {'name': 'علوان', 'number': '9', 'rating': '6.2', 'x': 0.75, 'y': 0.3},
    ];
  }

  // بناء لاعب بسيط
  Widget _buildSimplePlayer(Map<String, dynamic> player) {
    return SizedBox(
      width: 50,
      height: 60,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // صورة اللاعب
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: _getTeamColor(),
              border: Border.all(color: Colors.white, width: 2),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.3),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Stack(
              children: [
                // أيقونة اللاعب
                Center(
                  child: Icon(Icons.person, color: Colors.white, size: 20),
                ),

                // رقم اللاعب
                Positioned(
                  top: -2,
                  right: -2,
                  child: Container(
                    width: 16,
                    height: 16,
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.8),
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.white, width: 1),
                    ),
                    child: Center(
                      child: Text(
                        player['number'],
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 8,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 2),

          // تقييم اللاعب
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
            decoration: BoxDecoration(
              color: _getSimpleRatingColor(double.parse(player['rating'])),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.white, width: 1),
            ),
            child: Text(
              player['rating'],
              style: const TextStyle(
                color: Colors.white,
                fontSize: 8,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),

          const SizedBox(height: 1),

          // اسم اللاعب
          Flexible(
            child: Text(
              player['name'],
              style: const TextStyle(
                color: Colors.white,
                fontSize: 7,
                fontWeight: FontWeight.bold,
                shadows: [
                  Shadow(
                    color: Colors.black,
                    blurRadius: 2,
                    offset: Offset(1, 1),
                  ),
                ],
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  // الحصول على لون التقييم البسيط
  Color _getSimpleRatingColor(double rating) {
    if (rating >= 7.0) return Colors.green;
    if (rating >= 6.5) return Colors.blue;
    if (rating >= 6.0) return Colors.orange;
    return Colors.red;
  }

  // الحصول على لون الفريق
  Color _getTeamColor() {
    return _selectedTeam == 'home' ? Colors.red : Colors.blue;
  }
}

// Class لرسم خطوط الملعب مثل الصورة
class SimpleFieldPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withValues(alpha: 0.9)
      ..strokeWidth = 2.5
      ..style = PaintingStyle.stroke;

    final width = size.width;
    final height = size.height;

    // حدود الملعب الخارجية
    canvas.drawRect(Rect.fromLTWH(0, 0, width, height), paint);

    // خط المنتصف
    canvas.drawLine(Offset(0, height / 2), Offset(width, height / 2), paint);

    // دائرة المنتصف
    canvas.drawCircle(Offset(width / 2, height / 2), width * 0.12, paint);

    // نقطة المنتصف
    final centerPaint = Paint()
      ..color = Colors.white.withValues(alpha: 0.9)
      ..style = PaintingStyle.fill;
    canvas.drawCircle(Offset(width / 2, height / 2), 3, centerPaint);

    // منطقة الجزاء الكبيرة العلوية
    canvas.drawRect(
      Rect.fromLTWH(width * 0.2, 0, width * 0.6, height * 0.25),
      paint,
    );

    // منطقة الجزاء الصغيرة العلوية
    canvas.drawRect(
      Rect.fromLTWH(width * 0.35, 0, width * 0.3, height * 0.12),
      paint,
    );

    // منطقة الجزاء الكبيرة السفلية
    canvas.drawRect(
      Rect.fromLTWH(width * 0.2, height * 0.75, width * 0.6, height * 0.25),
      paint,
    );

    // منطقة الجزاء الصغيرة السفلية
    canvas.drawRect(
      Rect.fromLTWH(width * 0.35, height * 0.88, width * 0.3, height * 0.12),
      paint,
    );

    // أقواس منطقة الجزاء العلوية
    canvas.drawArc(
      Rect.fromCenter(
        center: Offset(width / 2, height * 0.25),
        width: width * 0.2,
        height: width * 0.2,
      ),
      0,
      3.14159,
      false,
      paint,
    );

    // أقواس منطقة الجزاء السفلية
    canvas.drawArc(
      Rect.fromCenter(
        center: Offset(width / 2, height * 0.75),
        width: width * 0.2,
        height: width * 0.2,
      ),
      3.14159,
      3.14159,
      false,
      paint,
    );
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

// Class لرسم خطوط الملعب
class CyberFieldPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withValues(alpha: 0.8)
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final width = size.width;
    final height = size.height;

    // حدود الملعب الخارجية
    canvas.drawRect(
      Rect.fromLTWH(width * 0.1, height * 0.1, width * 0.8, height * 0.8),
      paint,
    );

    // خط المنتصف
    canvas.drawLine(
      Offset(width * 0.1, height * 0.5),
      Offset(width * 0.9, height * 0.5),
      paint,
    );

    // دائرة المنتصف
    canvas.drawCircle(Offset(width / 2, height / 2), width * 0.1, paint);

    // منطقة الجزاء العلوية
    canvas.drawRect(
      Rect.fromLTWH(width * 0.3, height * 0.1, width * 0.4, height * 0.15),
      paint,
    );

    // منطقة الجزاء السفلية
    canvas.drawRect(
      Rect.fromLTWH(width * 0.3, height * 0.75, width * 0.4, height * 0.15),
      paint,
    );

    // نقطة الجزاء العلوية
    canvas.drawCircle(
      Offset(width / 2, height * 0.2),
      3,
      Paint()..color = Colors.white.withValues(alpha: 0.8),
    );

    // نقطة الجزاء السفلية
    canvas.drawCircle(
      Offset(width / 2, height * 0.8),
      3,
      Paint()..color = Colors.white.withValues(alpha: 0.8),
    );
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
