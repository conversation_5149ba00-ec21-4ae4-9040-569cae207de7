import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class SupabaseService {
  static const String supabaseUrl = 'https://vgryqmgpuxdbrcxdcovz.supabase.co';
  static const String supabaseAnonKey =
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZncnlxbWdwdXhkYnJjeGRjb3Z6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMxMTE1MTAsImV4cCI6MjA2ODY4NzUxMH0.LouR4IdzLvDsKSLQjvQeq_CwcoxYdEIamvCQ_K1bFW8';

  static SupabaseClient get client => Supabase.instance.client;

  // تهيئة Supabase
  static Future<void> initialize() async {
    await Supabase.initialize(url: supabaseUrl, anonKey: supabaseAnonKey);
  }

  // تسجيل الدخول
  static Future<AuthResponse> signIn({
    required String email,
    required String password,
  }) async {
    try {
      debugPrint('🔗 محاولة الاتصال بـ Supabase للدخول...');
      debugPrint('📧 البريد الإلكتروني: $email');

      final response = await client.auth.signInWithPassword(
        email: email,
        password: password,
      );

      debugPrint('✅ تم الاتصال بـ Supabase بنجاح');
      debugPrint('👤 معرف المستخدم: ${response.user?.id}');

      return response;
    } catch (e) {
      debugPrint('❌ خطأ في الاتصال بـ Supabase: $e');
      rethrow;
    }
  }

  // إنشاء حساب جديد
  static Future<AuthResponse> signUp({
    required String email,
    required String password,
  }) async {
    try {
      debugPrint('🔗 محاولة الاتصال بـ Supabase لإنشاء حساب...');
      debugPrint('📧 البريد الإلكتروني: $email');

      final response = await client.auth.signUp(
        email: email,
        password: password,
      );

      debugPrint('✅ تم إرسال طلب إنشاء الحساب');
      debugPrint('👤 معرف المستخدم: ${response.user?.id}');
      debugPrint('📧 حالة التأكيد: ${response.user?.emailConfirmedAt}');

      return response;
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء الحساب: $e');
      rethrow;
    }
  }

  // تسجيل الخروج
  static Future<void> signOut() async {
    try {
      await client.auth.signOut();
    } catch (e) {
      rethrow;
    }
  }

  // إعادة تعيين كلمة المرور
  static Future<void> resetPassword({required String email}) async {
    try {
      await client.auth.resetPasswordForEmail(email);
    } catch (e) {
      rethrow;
    }
  }

  // الحصول على المستخدم الحالي
  static User? get currentUser => client.auth.currentUser;

  // التحقق من حالة تسجيل الدخول
  static bool get isLoggedIn => currentUser != null;

  // الاستماع لتغييرات حالة المصادقة
  static Stream<AuthState> get authStateChanges =>
      client.auth.onAuthStateChange;

  // ==================== إدارة الأخبار ====================

  /// جلب جميع الأخبار
  static Future<List<Map<String, dynamic>>> getNews() async {
    try {
      debugPrint('📰 جلب الأخبار من Supabase...');

      final response = await client
          .from('news')
          .select()
          .order('created_at', ascending: false);

      debugPrint('✅ تم جلب ${response.length} خبر');
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      debugPrint('❌ خطأ في جلب الأخبار: $e');
      return [];
    }
  }

  /// إضافة خبر جديد
  static Future<bool> addNews({
    required String title,
    required String content,
    required String category,
    String? imageUrl,
    String? author,
    bool isBreaking = false,
    List<String>? tags,
  }) async {
    try {
      debugPrint('📝 إضافة خبر جديد...');

      await client.from('news').insert({
        'title': title,
        'content': content,
        'category': category,
        'image_url': imageUrl,
        'author': author ?? 'مجهول',
        'is_breaking': isBreaking,
        'tags': tags,
        'created_at': DateTime.now().toIso8601String(),
        'user_id': currentUser?.id,
      });

      debugPrint('✅ تم إضافة الخبر بنجاح');
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في إضافة الخبر: $e');
      return false;
    }
  }

  /// تحديث خبر
  static Future<bool> updateNews({
    required String newsId,
    String? title,
    String? content,
    String? category,
    String? imageUrl,
    bool? isBreaking,
    List<String>? tags,
  }) async {
    try {
      debugPrint('📝 تحديث الخبر $newsId...');

      Map<String, dynamic> updateData = {
        'updated_at': DateTime.now().toIso8601String(),
      };

      if (title != null) updateData['title'] = title;
      if (content != null) updateData['content'] = content;
      if (category != null) updateData['category'] = category;
      if (imageUrl != null) updateData['image_url'] = imageUrl;
      if (isBreaking != null) updateData['is_breaking'] = isBreaking;
      if (tags != null) updateData['tags'] = tags;

      await client.from('news').update(updateData).eq('id', newsId);

      debugPrint('✅ تم تحديث الخبر بنجاح');
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في تحديث الخبر: $e');
      return false;
    }
  }

  /// حذف خبر
  static Future<bool> deleteNews(String newsId) async {
    try {
      debugPrint('🗑️ حذف الخبر $newsId...');

      await client.from('news').delete().eq('id', newsId);

      debugPrint('✅ تم حذف الخبر بنجاح');
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في حذف الخبر: $e');
      return false;
    }
  }

  // ==================== إدارة الإشعارات ====================

  /// جلب الإشعارات للمستخدم الحالي
  static Future<List<Map<String, dynamic>>> getNotifications() async {
    try {
      debugPrint('🔔 جلب الإشعارات من Supabase...');

      final response = await client
          .from('notifications')
          .select()
          .eq('user_id', currentUser?.id ?? '')
          .order('created_at', ascending: false);

      debugPrint('✅ تم جلب ${response.length} إشعار');
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      debugPrint('❌ خطأ في جلب الإشعارات: $e');
      return [];
    }
  }

  /// إضافة إشعار جديد
  static Future<bool> addNotification({
    required String title,
    required String message,
    String? type,
    Map<String, dynamic>? data,
  }) async {
    try {
      debugPrint('🔔 إضافة إشعار جديد...');

      await client.from('notifications').insert({
        'title': title,
        'message': message,
        'type': type ?? 'general',
        'data': data,
        'user_id': currentUser?.id,
        'is_read': false,
        'created_at': DateTime.now().toIso8601String(),
      });

      debugPrint('✅ تم إضافة الإشعار بنجاح');
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في إضافة الإشعار: $e');
      return false;
    }
  }

  /// تحديد الإشعار كمقروء
  static Future<bool> markNotificationAsRead(String notificationId) async {
    try {
      await client
          .from('notifications')
          .update({'is_read': true})
          .eq('id', notificationId);

      return true;
    } catch (e) {
      debugPrint('❌ خطأ في تحديث الإشعار: $e');
      return false;
    }
  }
}
