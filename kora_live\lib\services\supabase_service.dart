import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class SupabaseService {
  static const String supabaseUrl = 'https://vgryqmgpuxdbrcxdcovz.supabase.co';
  static const String supabaseAnonKey =
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZncnlxbWdwdXhkYnJjeGRjb3Z6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMxMTE1MTAsImV4cCI6MjA2ODY4NzUxMH0.LouR4IdzLvDsKSLQjvQeq_CwcoxYdEIamvCQ_K1bFW8';

  static SupabaseClient get client => Supabase.instance.client;

  // تهيئة Supabase
  static Future<void> initialize() async {
    await Supabase.initialize(url: supabaseUrl, anonKey: supabaseAnonKey);
  }

  // تسجيل الدخول
  static Future<AuthResponse> signIn({
    required String email,
    required String password,
  }) async {
    try {
      debugPrint('🔗 محاولة الاتصال بـ Supabase للدخول...');
      debugPrint('📧 البريد الإلكتروني: $email');

      final response = await client.auth.signInWithPassword(
        email: email,
        password: password,
      );

      debugPrint('✅ تم الاتصال بـ Supabase بنجاح');
      debugPrint('👤 معرف المستخدم: ${response.user?.id}');

      return response;
    } catch (e) {
      debugPrint('❌ خطأ في الاتصال بـ Supabase: $e');
      rethrow;
    }
  }

  // إنشاء حساب جديد
  static Future<AuthResponse> signUp({
    required String email,
    required String password,
  }) async {
    try {
      debugPrint('🔗 محاولة الاتصال بـ Supabase لإنشاء حساب...');
      debugPrint('📧 البريد الإلكتروني: $email');

      final response = await client.auth.signUp(
        email: email,
        password: password,
      );

      debugPrint('✅ تم إرسال طلب إنشاء الحساب');
      debugPrint('👤 معرف المستخدم: ${response.user?.id}');
      debugPrint('📧 حالة التأكيد: ${response.user?.emailConfirmedAt}');

      return response;
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء الحساب: $e');
      rethrow;
    }
  }

  // تسجيل الخروج
  static Future<void> signOut() async {
    try {
      await client.auth.signOut();
    } catch (e) {
      rethrow;
    }
  }

  // إعادة تعيين كلمة المرور
  static Future<void> resetPassword({required String email}) async {
    try {
      await client.auth.resetPasswordForEmail(email);
    } catch (e) {
      rethrow;
    }
  }

  // الحصول على المستخدم الحالي
  static User? get currentUser => client.auth.currentUser;

  // التحقق من حالة تسجيل الدخول
  static bool get isLoggedIn => currentUser != null;

  // الاستماع لتغييرات حالة المصادقة
  static Stream<AuthState> get authStateChanges =>
      client.auth.onAuthStateChange;
}
