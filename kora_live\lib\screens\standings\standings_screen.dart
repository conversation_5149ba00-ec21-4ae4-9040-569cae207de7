import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:provider/provider.dart';
import '../../constants/app_colors.dart';
import '../../services/api_service.dart';
import '../../providers/language_provider.dart';
import '../../utils/translation_extension.dart';
import '../../widgets/animations/animated_widgets.dart';
import '../details/team_details_screen.dart';

class StandingsScreen extends StatefulWidget {
  const StandingsScreen({super.key});

  @override
  State<StandingsScreen> createState() => _StandingsScreenState();
}

class _StandingsScreenState extends State<StandingsScreen> {
  List<Map<String, dynamic>> standings = [];
  bool isLoading = true;
  String selectedLeague = 'الدوري المصري';

  final List<String> leagues = [
    'الدوري المصري',
    'الدوري الإنجليزي',
    'الليجا الإسبانية',
    'البوندسليجا',
    'الدوري الإيطالي',
  ];

  @override
  void initState() {
    super.initState();
    _loadStandings();
  }

  Future<void> _loadStandings() async {
    setState(() {
      isLoading = true;
    });

    try {
      final data = await ApiService.getLeagueStandings(selectedLeague);
      setState(() {
        standings = data;
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل البيانات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<LanguageProvider>(
      builder: (context, languageProvider, child) {
        return Scaffold(
          backgroundColor: Theme.of(context).scaffoldBackgroundColor,
          appBar: AppBar(
            title: Text(
              context.tr('league_standings'),
              style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 20),
            ),
            backgroundColor: Theme.of(context).primaryColor,
            foregroundColor: Colors.white,
            elevation: 0,
            actions: [
              IconButton(
                icon: const Icon(FontAwesomeIcons.arrowsRotate),
                onPressed: _loadStandings,
              ),
            ],
          ),
          body: Column(
            children: [
              // شريط اختيار الدوري
              Container(
                height: 60,
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: leagues.length,
                  itemBuilder: (context, index) {
                    final league = leagues[index];
                    final isSelected = league == selectedLeague;

                    return Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: FilterChip(
                        label: Text(
                          league,
                          style: TextStyle(
                            color: isSelected
                                ? Colors.white
                                : AppColors.textPrimary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        selected: isSelected,
                        onSelected: (selected) {
                          if (selected) {
                            setState(() {
                              selectedLeague = league;
                            });
                            _loadStandings();
                          }
                        },
                        backgroundColor: Theme.of(context).cardColor,
                        selectedColor: Theme.of(context).primaryColor,
                        checkmarkColor: Colors.white,
                      ),
                    );
                  },
                ),
              ),

              // جدول الترتيب
              Expanded(
                child: isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : standings.isEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              FontAwesomeIcons.trophy,
                              size: 64,
                              color: Colors.grey[400],
                            ),
                            const SizedBox(height: 16),
                            Text(
                              context.tr('no_standings_available'),
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      )
                    : RefreshIndicator(
                        onRefresh: _loadStandings,
                        child: ListView(
                          padding: const EdgeInsets.all(16),
                          children: [
                            // عنوان الجدول
                            SlideInAnimation(
                              delay: const Duration(milliseconds: 100),
                              child: Container(
                                padding: const EdgeInsets.all(16),
                                decoration: BoxDecoration(
                                  color: Theme.of(context).primaryColor,
                                  borderRadius: const BorderRadius.only(
                                    topLeft: Radius.circular(12),
                                    topRight: Radius.circular(12),
                                  ),
                                ),
                                child: Row(
                                  children: [
                                    const SizedBox(width: 40),
                                    Expanded(
                                      flex: 3,
                                      child: Text(
                                        context.tr('team'),
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                    SizedBox(
                                      width: 30,
                                      child: Text(
                                        context.tr('played'),
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontWeight: FontWeight.bold,
                                          fontSize: 12,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    SizedBox(
                                      width: 30,
                                      child: Text(
                                        context.tr('won'),
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontWeight: FontWeight.bold,
                                          fontSize: 12,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    SizedBox(
                                      width: 30,
                                      child: Text(
                                        context.tr('drawn'),
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontWeight: FontWeight.bold,
                                          fontSize: 12,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    SizedBox(
                                      width: 30,
                                      child: Text(
                                        context.tr('lost'),
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontWeight: FontWeight.bold,
                                          fontSize: 12,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    SizedBox(
                                      width: 40,
                                      child: Text(
                                        context.tr('points'),
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontWeight: FontWeight.bold,
                                          fontSize: 12,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),

                            // قائمة الفرق
                            ...standings.asMap().entries.map((entry) {
                              final index = entry.key;
                              final team = entry.value;

                              return SlideInAnimation(
                                delay: Duration(
                                  milliseconds: 200 + (index * 50),
                                ),
                                child: _buildTeamRow(team, index),
                              );
                            }),
                          ],
                        ),
                      ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildTeamRow(Map<String, dynamic> team, int index) {
    Color? backgroundColor;
    if ((team['position'] ?? 0) <= 3) {
      backgroundColor = AppColors.primaryGreen.withValues(alpha: 0.1);
    } else if ((team['position'] ?? 0) >= standings.length - 2) {
      backgroundColor = Colors.red.withValues(alpha: 0.1);
    }

    return Container(
      decoration: BoxDecoration(
        color: backgroundColor ?? Theme.of(context).cardColor,
        border: Border(
          bottom: BorderSide(
            color: Colors.grey.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
      ),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => TeamDetailsScreen(team: team),
            ),
          );
        },
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          child: Row(
            children: [
              // المركز
              Container(
                width: 30,
                height: 30,
                decoration: BoxDecoration(
                  color: _getPositionColor(team['position'] ?? 0),
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Center(
                  child: Text(
                    '${team['position'] ?? 0}',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 10),

              // اسم الفريق
              Expanded(
                flex: 3,
                child: Row(
                  children: [
                    Text(
                      team['logo'] ?? '⚽',
                      style: const TextStyle(fontSize: 20),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        team['team'] ?? 'فريق',
                        style: const TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 14,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // الإحصائيات
              SizedBox(
                width: 30,
                child: Text(
                  '${team['played'] ?? 0}',
                  textAlign: TextAlign.center,
                  style: const TextStyle(fontSize: 12),
                ),
              ),
              const SizedBox(width: 8),
              SizedBox(
                width: 30,
                child: Text(
                  '${team['won'] ?? 0}',
                  textAlign: TextAlign.center,
                  style: const TextStyle(fontSize: 12, color: Colors.green),
                ),
              ),
              const SizedBox(width: 8),
              SizedBox(
                width: 30,
                child: Text(
                  '${team['drawn'] ?? 0}',
                  textAlign: TextAlign.center,
                  style: const TextStyle(fontSize: 12, color: Colors.orange),
                ),
              ),
              const SizedBox(width: 8),
              SizedBox(
                width: 30,
                child: Text(
                  '${team['lost'] ?? 0}',
                  textAlign: TextAlign.center,
                  style: const TextStyle(fontSize: 12, color: Colors.red),
                ),
              ),
              const SizedBox(width: 8),
              Container(
                width: 40,
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 4),
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  '${team['points'] ?? 0}',
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getPositionColor(int position) {
    if (position <= 3) {
      return AppColors.primaryGreen;
    } else if (position <= 6) {
      return AppColors.accentOrange;
    } else {
      return Colors.grey;
    }
  }
}
