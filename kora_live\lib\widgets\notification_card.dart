import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../constants/app_colors.dart';
import '../constants/app_constants.dart';
import '../models/notification_model.dart';

class NotificationCard extends StatelessWidget {
  final NotificationModel notification;
  final VoidCallback? onTap;
  final VoidCallback? onDismiss;

  const NotificationCard({
    super.key,
    required this.notification,
    this.onTap,
    this.onDismiss,
  });

  @override
  Widget build(BuildContext context) {
    return Dismissible(
      key: Key(notification.id),
      direction: DismissDirection.endToStart,
      onDismissed: (direction) => onDismiss?.call(),
      background: Container(
        alignment: Alignment.centerRight,
        padding: const EdgeInsets.only(right: AppConstants.defaultPadding),
        decoration: BoxDecoration(
          color: AppColors.error,
          borderRadius: BorderRadius.circular(AppConstants.defaultRadius),
        ),
        child: const Icon(
          FontAwesomeIcons.trash,
          color: AppColors.textLight,
          size: 20,
        ),
      ),
      child: Card(
        elevation: notification.isRead ? 1 : 3,
        shadowColor: AppColors.shadowLight,
        color: notification.isRead 
            ? AppColors.cardBackground 
            : AppColors.cardBackground,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.defaultRadius),
          side: notification.isRead 
              ? BorderSide.none 
              : const BorderSide(
                  color: AppColors.primaryGreen,
                  width: 1,
                ),
        ),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(AppConstants.defaultRadius),
          child: Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildIcon(),
                const SizedBox(width: AppConstants.defaultPadding),
                Expanded(child: _buildContent(context)),
                const SizedBox(width: AppConstants.smallPadding),
                _buildTrailing(context),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildIcon() {
    IconData iconData;
    Color iconColor;

    switch (notification.type) {
      case NotificationType.news:
        iconData = FontAwesomeIcons.newspaper;
        iconColor = AppColors.info;
        break;
      case NotificationType.match:
        iconData = FontAwesomeIcons.futbol;
        iconColor = AppColors.primaryGreen;
        break;
      case NotificationType.goal:
        iconData = FontAwesomeIcons.bullseye;
        iconColor = AppColors.accentOrange;
        break;
      case NotificationType.transfer:
        iconData = FontAwesomeIcons.arrowRightArrowLeft;
        iconColor = AppColors.accentBlue;
        break;
      case NotificationType.general:
        iconData = FontAwesomeIcons.bell;
        iconColor = AppColors.textMuted;
        break;
    }

    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: iconColor.withValues(alpha: 0.1),
        shape: BoxShape.circle,
      ),
      child: Icon(
        iconData,
        size: 18,
        color: iconColor,
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: Text(
                notification.title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: notification.isRead ? FontWeight.normal : FontWeight.bold,
                  color: notification.isRead ? AppColors.textSecondary : AppColors.textPrimary,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            if (!notification.isRead)
              Container(
                width: 8,
                height: 8,
                margin: const EdgeInsets.only(left: AppConstants.smallPadding),
                decoration: const BoxDecoration(
                  color: AppColors.primaryGreen,
                  shape: BoxShape.circle,
                ),
              ),
          ],
        ),
        const SizedBox(height: AppConstants.smallPadding),
        Text(
          notification.body,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: AppColors.textSecondary,
            height: 1.4,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: AppConstants.smallPadding),
        Row(
          children: [
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppConstants.smallPadding,
                vertical: 2,
              ),
              decoration: BoxDecoration(
                color: _getTypeColor().withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppConstants.smallRadius),
              ),
              child: Text(
                notification.typeDisplayName,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: _getTypeColor(),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            const SizedBox(width: AppConstants.smallPadding),
            Icon(
              FontAwesomeIcons.clock,
              size: 12,
              color: AppColors.textMuted,
            ),
            const SizedBox(width: 4),
            Text(
              notification.timeAgo,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppColors.textMuted,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildTrailing(BuildContext context) {
    return Column(
      children: [
        if (notification.imageUrl != null)
          ClipRRect(
            borderRadius: BorderRadius.circular(AppConstants.smallRadius),
            child: CachedNetworkImage(
              imageUrl: notification.imageUrl!,
              width: 50,
              height: 50,
              fit: BoxFit.cover,
              placeholder: (context, url) => Container(
                width: 50,
                height: 50,
                color: AppColors.textMuted.withValues(alpha: 0.3),
                child: const Icon(
                  FontAwesomeIcons.image,
                  size: 20,
                  color: AppColors.textMuted,
                ),
              ),
              errorWidget: (context, url, error) => Container(
                width: 50,
                height: 50,
                color: AppColors.textMuted.withValues(alpha: 0.3),
                child: const Icon(
                  FontAwesomeIcons.image,
                  size: 20,
                  color: AppColors.textMuted,
                ),
              ),
            ),
          )
        else
          const SizedBox(width: 50),
      ],
    );
  }

  Color _getTypeColor() {
    switch (notification.type) {
      case NotificationType.news:
        return AppColors.info;
      case NotificationType.match:
        return AppColors.primaryGreen;
      case NotificationType.goal:
        return AppColors.accentOrange;
      case NotificationType.transfer:
        return AppColors.accentBlue;
      case NotificationType.general:
        return AppColors.textMuted;
    }
  }
}
