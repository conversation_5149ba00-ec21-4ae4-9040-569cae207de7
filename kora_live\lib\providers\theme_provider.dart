import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/app_themes.dart';

enum CustomThemeMode { dark, unified }

class ThemeProvider extends ChangeNotifier {
  static const String _themeKey = 'custom_theme_mode';

  CustomThemeMode _customThemeMode = CustomThemeMode.dark;
  bool _isLoading = false;

  CustomThemeMode get customThemeMode => _customThemeMode;
  bool get isLoading => _isLoading;
  bool get isDarkMode => _customThemeMode == CustomThemeMode.dark;
  bool get isUnifiedMode => _customThemeMode == CustomThemeMode.unified;

  // Get the current theme data
  ThemeData get currentTheme {
    switch (_customThemeMode) {
      case CustomThemeMode.unified:
        return AppThemes.unifiedTheme; // Unified theme
      case CustomThemeMode.dark:
        return AppThemes.darkTheme; // Traditional dark theme
    }
  }

  // For compatibility with MaterialApp - always return system
  ThemeMode get themeMode => ThemeMode.system;

  // Get the appropriate theme data
  ThemeData getThemeData(bool isDark) {
    return currentTheme; // Always return current theme regardless of isDark
  }

  ThemeProvider() {
    _loadTheme();
  }

  Future<void> _loadTheme() async {
    _isLoading = true;
    notifyListeners();

    try {
      final prefs = await SharedPreferences.getInstance();
      final themeIndex = prefs.getInt(_themeKey) ?? 0;
      if (themeIndex < CustomThemeMode.values.length) {
        _customThemeMode = CustomThemeMode.values[themeIndex];
      }
    } catch (e) {
      _customThemeMode = CustomThemeMode.dark;
    }

    _isLoading = false;
    notifyListeners();
  }

  Future<void> setCustomThemeMode(CustomThemeMode mode) async {
    if (_customThemeMode == mode) return;

    _customThemeMode = mode;
    notifyListeners();

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_themeKey, mode.index);
    } catch (e) {
      // Handle error silently
    }
  }

  Future<void> toggleTheme() async {
    CustomThemeMode newMode;
    switch (_customThemeMode) {
      case CustomThemeMode.dark:
        newMode = CustomThemeMode.unified;
        break;
      case CustomThemeMode.unified:
        newMode = CustomThemeMode.dark;
        break;
    }
    await setCustomThemeMode(newMode);
  }
}
