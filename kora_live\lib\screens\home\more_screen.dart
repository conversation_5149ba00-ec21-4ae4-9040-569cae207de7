import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:provider/provider.dart';
import '../../constants/app_colors.dart';
import '../../providers/auth_provider.dart';
import '../../providers/theme_provider.dart';
import '../../providers/language_provider.dart';
import '../../widgets/animations/animated_widgets.dart';
import '../auth/login_screen.dart';
import '../settings/general_settings_screen.dart';
import '../../utils/translation_extension.dart';

class MoreScreen extends StatelessWidget {
  const MoreScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<LanguageProvider>(
      builder: (context, languageProvider, child) {
        final authProvider = Provider.of<AuthProvider>(context);
        final themeProvider = Provider.of<ThemeProvider>(context);

        return Scaffold(
          backgroundColor: Theme.of(context).scaffoldBackgroundColor,
          body: Safe<PERSON>rea(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 20),

                  // عنوان الصفحة
                  FadeInAnimation(
                    child: Text(
                      context.tr('more'),
                      style: Theme.of(context).textTheme.displayMedium,
                    ),
                  ),

                  const SizedBox(height: 30),

                  // قسم تسجيل الدخول/الحساب
                  SlideInAnimation(
                    delay: const Duration(milliseconds: 200),
                    child: _buildAccountSection(context, authProvider),
                  ),

                  const SizedBox(height: 20),

                  // قسم المظهر
                  SlideInAnimation(
                    delay: const Duration(milliseconds: 400),
                    child: _buildThemeSection(context, themeProvider),
                  ),

                  const SizedBox(height: 20),

                  // قائمة الخيارات
                  _buildMenuSection(
                    title: context.tr('settings'),
                    context: context,
                    items: [
                      _MenuItem(
                        icon: FontAwesomeIcons.gear,
                        title: context.tr('general_settings'),
                        subtitle: context.tr('customize_app'),
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) =>
                                  const GeneralSettingsScreen(),
                            ),
                          );
                        },
                      ),
                      _MenuItem(
                        icon: FontAwesomeIcons.bell,
                        title: context.tr('notification_settings'),
                        subtitle: context.tr('notifications_description'),
                        onTap: () {},
                      ),
                      _MenuItem(
                        icon: FontAwesomeIcons.palette,
                        title: context.tr('theme'),
                        subtitle: context.tr('appearance'),
                        onTap: () {},
                      ),
                    ],
                  ),

                  const SizedBox(height: 20),

                  _buildMenuSection(
                    title: 'المحتوى',
                    context: context,
                    items: [
                      _MenuItem(
                        icon: FontAwesomeIcons.heart,
                        title: 'المفضلة',
                        subtitle: 'الفرق والأخبار المفضلة لديك',
                        onTap: () {},
                      ),
                      _MenuItem(
                        icon: FontAwesomeIcons.download,
                        title: 'التحميلات',
                        subtitle: 'الملفات والفيديوهات المحملة',
                        onTap: () {},
                      ),
                      _MenuItem(
                        icon: FontAwesomeIcons.clock,
                        title: 'السجل',
                        subtitle: 'تاريخ المشاهدة والتصفح',
                        onTap: () {},
                      ),
                    ],
                  ),

                  const SizedBox(height: 20),

                  _buildMenuSection(
                    title: context.tr('contact_us'),
                    context: context,
                    items: [
                      _MenuItem(
                        icon: FontAwesomeIcons.circleQuestion,
                        title: context.tr('about_app'),
                        subtitle: context.tr('privacy_policy'),
                        onTap: () {},
                      ),
                      _MenuItem(
                        icon: FontAwesomeIcons.envelope,
                        title: context.tr('contact_us'),
                        subtitle: context.tr('terms_of_service'),
                        onTap: () {},
                      ),
                      _MenuItem(
                        icon: FontAwesomeIcons.star,
                        title: context.tr('rate_app'),
                        subtitle: context.tr('share_app'),
                        onTap: () {},
                      ),
                    ],
                  ),

                  const SizedBox(height: 30),

                  // معلومات التطبيق
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: Theme.of(context).cardColor,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          blurRadius: 10,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        Icon(
                          FontAwesomeIcons.futbol,
                          size: 40,
                          color: Theme.of(context).primaryColor,
                        ),
                        const SizedBox(height: 12),
                        Text(
                          context.tr('app_name'),
                          style: Theme.of(context).textTheme.titleLarge
                              ?.copyWith(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                              ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '${context.tr('version')} 1.0.0',
                          style: Theme.of(
                            context,
                          ).textTheme.bodyMedium?.copyWith(fontSize: 14),
                        ),
                      ],
                    ),
                  ),
                  // إضافة مساحة إضافية في النهاية لتجنب overflow
                  const SizedBox(height: 50),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildMenuSection({
    required String title,
    required List<_MenuItem> items,
    required BuildContext context,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: items
                .map((item) => _buildMenuItem(item, context))
                .toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildMenuItem(_MenuItem item, BuildContext context) {
    return ListTile(
      dense: true,
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(item.icon, color: Theme.of(context).primaryColor, size: 18),
      ),
      title: Text(
        item.title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.w600,
          fontSize: 14,
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      subtitle: Text(
        item.subtitle,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(fontSize: 12),
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
      ),
      trailing: Icon(
        FontAwesomeIcons.chevronLeft,
        size: 14,
        color: Theme.of(context).iconTheme.color,
      ),
      onTap: item.onTap,
    );
  }

  Widget _buildAccountSection(BuildContext context, AuthProvider authProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          context.tr('account_settings'),
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.3),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: authProvider.isLoggedIn
              ? _buildLoggedInSection(context, authProvider)
              : _buildGuestSection(context),
        ),
      ],
    );
  }

  Widget _buildLoggedInSection(
    BuildContext context,
    AuthProvider authProvider,
  ) {
    return Column(
      children: [
        // معلومات المستخدم
        ListTile(
          dense: true,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 8,
          ),
          leading: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppColors.primaryGreen.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              FontAwesomeIcons.user,
              color: Theme.of(context).iconTheme.color,
              size: 18,
            ),
          ),
          title: Text(
            context.tr('welcome'),
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
              fontSize: 14,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          subtitle: Text(
            authProvider.currentUser?.email ?? 'مستخدم',
            style: Theme.of(
              context,
            ).textTheme.bodySmall?.copyWith(fontSize: 12),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),

        const Divider(height: 1),

        // تسجيل الخروج
        ListTile(
          leading: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppColors.accentOrange.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              FontAwesomeIcons.rightFromBracket,
              color: Theme.of(context).iconTheme.color,
              size: 20,
            ),
          ),
          title: Text(
            context.tr('logout'),
            style: TextStyle(
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          subtitle: Text(
            context.tr('logout'),
            style: TextStyle(color: AppColors.textSecondary, fontSize: 12),
          ),
          trailing: Icon(
            FontAwesomeIcons.chevronLeft,
            size: 16,
            color: Theme.of(context).iconTheme.color,
          ),
          onTap: () async {
            // تأكيد تسجيل الخروج
            final shouldLogout = await showDialog<bool>(
              context: context,
              builder: (context) => AlertDialog(
                title: Text(context.tr('logout')),
                content: Text(context.tr('logout_confirm_message')),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(false),
                    child: Text(context.tr('cancel')),
                  ),
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(true),
                    child: Text(context.tr('logout')),
                  ),
                ],
              ),
            );

            if (shouldLogout == true) {
              await authProvider.logout();
            }
          },
        ),
      ],
    );
  }

  Widget _buildGuestSection(BuildContext context) {
    return Column(
      children: [
        // رسالة ترحيب للضيف
        ListTile(
          leading: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppColors.primaryGreen.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              FontAwesomeIcons.userLarge,
              color: Theme.of(context).iconTheme.color,
              size: 20,
            ),
          ),
          title: Text(
            context.tr('guest_welcome'),
            style: TextStyle(
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          subtitle: Text(
            context.tr('guest_login_message'),
            style: TextStyle(color: AppColors.textSecondary, fontSize: 12),
          ),
        ),

        const Divider(height: 1),

        // تسجيل الدخول
        ListTile(
          leading: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppColors.primaryGreen.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              FontAwesomeIcons.rightToBracket,
              color: Theme.of(context).iconTheme.color,
              size: 20,
            ),
          ),
          title: Text(
            context.tr('login'),
            style: TextStyle(
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          subtitle: Text(
            context.tr('login_message'),
            style: TextStyle(color: AppColors.textSecondary, fontSize: 12),
          ),
          trailing: Icon(
            FontAwesomeIcons.chevronLeft,
            size: 16,
            color: Theme.of(context).iconTheme.color,
          ),
          onTap: () {
            Navigator.of(context).push(
              MaterialPageRoute(builder: (context) => const LoginScreen()),
            );
          },
        ),
      ],
    );
  }

  Widget _buildThemeSection(BuildContext context, ThemeProvider themeProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          context.tr('theme'),
          style: Theme.of(context).textTheme.headlineSmall,
        ),
        const SizedBox(height: 12),
        Container(
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.3),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: IntrinsicHeight(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // الوضع الفاتح (الموحد)
                ListTile(
                  dense: true,
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 4,
                  ),
                  leading: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.blue.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      FontAwesomeIcons.palette,
                      color: Theme.of(context).iconTheme.color,
                      size: 18,
                    ),
                  ),
                  title: Text(
                    context.tr('unified_mode'),
                    style: Theme.of(
                      context,
                    ).textTheme.titleMedium?.copyWith(fontSize: 14),
                  ),
                  subtitle: Text(
                    context.tr('unified_mode_description'),
                    style: Theme.of(
                      context,
                    ).textTheme.bodySmall?.copyWith(fontSize: 12),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  trailing: Radio<CustomThemeMode>(
                    value: CustomThemeMode.unified,
                    groupValue: themeProvider.customThemeMode,
                    onChanged: (value) {
                      if (value != null) {
                        themeProvider.setCustomThemeMode(value);
                      }
                    },
                    activeColor: AppColors.accentOrange,
                    materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                  onTap: () =>
                      themeProvider.setCustomThemeMode(CustomThemeMode.unified),
                ),

                const Divider(height: 1),

                // الوضع المظلم
                ListTile(
                  dense: true,
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 4,
                  ),
                  leading: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.indigo.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      FontAwesomeIcons.moon,
                      color: Theme.of(context).iconTheme.color,
                      size: 18,
                    ),
                  ),
                  title: Text(
                    context.tr('dark_mode'),
                    style: Theme.of(
                      context,
                    ).textTheme.titleMedium?.copyWith(fontSize: 14),
                  ),
                  subtitle: Text(
                    context.tr('dark_mode_description'),
                    style: Theme.of(
                      context,
                    ).textTheme.bodySmall?.copyWith(fontSize: 12),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  trailing: Radio<CustomThemeMode>(
                    value: CustomThemeMode.dark,
                    groupValue: themeProvider.customThemeMode,
                    onChanged: (value) {
                      if (value != null) {
                        themeProvider.setCustomThemeMode(value);
                      }
                    },
                    activeColor: AppColors.accentOrange,
                    materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                  onTap: () =>
                      themeProvider.setCustomThemeMode(CustomThemeMode.dark),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

class _MenuItem {
  final IconData icon;
  final String title;
  final String subtitle;
  final VoidCallback onTap;

  _MenuItem({
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.onTap,
  });
}
