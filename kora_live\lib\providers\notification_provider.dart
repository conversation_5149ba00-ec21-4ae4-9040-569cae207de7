import 'package:flutter/foundation.dart';
import '../models/notification_model.dart';
import '../utils/dummy_data.dart';

class NotificationProvider extends ChangeNotifier {
  List<NotificationModel> _notifications = [];
  bool _isLoading = false;
  String? _error;

  List<NotificationModel> get notifications => _notifications;
  bool get isLoading => _isLoading;
  String? get error => _error;

  int get unreadCount => _notifications.where((n) => !n.isRead).length;
  bool get hasUnreadNotifications => unreadCount > 0;

  NotificationProvider() {
    loadNotifications();
  }

  Future<void> loadNotifications() async {
    _setLoading(true);
    _error = null;
    
    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));
      
      _notifications = DummyData.getDummyNotifications();
      
      notifyListeners();
    } catch (e) {
      _error = 'حدث خطأ في تحميل الإشعارات';
      debugPrint('Error loading notifications: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> refreshNotifications() async {
    await loadNotifications();
  }

  void markAsRead(String notificationId) {
    final index = _notifications.indexWhere((n) => n.id == notificationId);
    if (index != -1) {
      _notifications[index] = _notifications[index].copyWith(isRead: true);
      notifyListeners();
    }
  }

  void markAllAsRead() {
    _notifications = _notifications.map((n) => n.copyWith(isRead: true)).toList();
    notifyListeners();
  }

  void deleteNotification(String notificationId) {
    _notifications.removeWhere((n) => n.id == notificationId);
    notifyListeners();
  }

  void clearAllNotifications() {
    _notifications.clear();
    notifyListeners();
  }

  List<NotificationModel> getNotificationsByType(NotificationType type) {
    return _notifications.where((n) => n.type == type).toList();
  }

  List<NotificationModel> getUnreadNotifications() {
    return _notifications.where((n) => !n.isRead).toList();
  }

  List<NotificationModel> getRecentNotifications({int limit = 5}) {
    final sortedNotifications = List<NotificationModel>.from(_notifications);
    sortedNotifications.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    return sortedNotifications.take(limit).toList();
  }

  void addNotification(NotificationModel notification) {
    _notifications.insert(0, notification);
    notifyListeners();
  }

  // Simulate receiving a new notification
  void simulateNewNotification() {
    final newNotification = NotificationModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: 'إشعار جديد',
      body: 'هذا إشعار تجريبي جديد',
      type: NotificationType.general,
      createdAt: DateTime.now(),
      isRead: false,
    );
    
    addNotification(newNotification);
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
}
