import 'package:flutter/foundation.dart';
import '../models/notification_model.dart';
import '../utils/dummy_data.dart';
import '../services/supabase_service.dart';

class NotificationProvider extends ChangeNotifier {
  List<NotificationModel> _notifications = [];
  bool _isLoading = false;
  String? _error;

  List<NotificationModel> get notifications => _notifications;
  bool get isLoading => _isLoading;
  String? get error => _error;

  int get unreadCount => _notifications.where((n) => !n.isRead).length;
  bool get hasUnreadNotifications => unreadCount > 0;

  NotificationProvider() {
    loadNotifications();
  }

  Future<void> loadNotifications() async {
    _setLoading(true);
    _error = null;

    try {
      debugPrint('🔔 تحميل الإشعارات من Supabase...');

      // محاولة جلب الإشعارات من Supabase
      final supabaseNotifications = await SupabaseService.getNotifications();

      if (supabaseNotifications.isNotEmpty) {
        // تحويل البيانات من Supabase إلى NotificationModel
        _notifications = supabaseNotifications.map((notificationData) {
          return NotificationModel(
            id: notificationData['id'].toString(),
            title: notificationData['title'] ?? 'إشعار',
            body: notificationData['message'] ?? 'رسالة الإشعار',
            type: _parseNotificationType(notificationData['type']),
            createdAt:
                DateTime.tryParse(notificationData['created_at'] ?? '') ??
                DateTime.now(),
            isRead: notificationData['is_read'] ?? false,
            data: notificationData['data'],
          );
        }).toList();

        debugPrint('✅ تم تحميل ${_notifications.length} إشعار من Supabase');
      } else {
        // في حالة عدم وجود إشعارات في قاعدة البيانات، استخدم البيانات التجريبية
        debugPrint(
          '⚠️ لا توجد إشعارات في قاعدة البيانات، استخدام البيانات التجريبية',
        );
        _notifications = DummyData.getDummyNotifications();
      }

      notifyListeners();
    } catch (e) {
      _error = 'حدث خطأ في تحميل الإشعارات';
      debugPrint('❌ خطأ في تحميل الإشعارات: $e');

      // في حالة الخطأ، استخدم البيانات التجريبية
      _notifications = DummyData.getDummyNotifications();
      notifyListeners();
    } finally {
      _setLoading(false);
    }
  }

  // تحويل نوع الإشعار من النص إلى enum
  NotificationType _parseNotificationType(String? type) {
    switch (type?.toLowerCase()) {
      case 'match':
        return NotificationType.match;
      case 'news':
        return NotificationType.news;
      case 'goal':
        return NotificationType.goal;
      case 'transfer':
        return NotificationType.transfer;
      default:
        return NotificationType.general;
    }
  }

  Future<void> refreshNotifications() async {
    await loadNotifications();
  }

  Future<void> markAsRead(String notificationId) async {
    final index = _notifications.indexWhere((n) => n.id == notificationId);
    if (index != -1) {
      // تحديث محلياً أولاً
      _notifications[index] = _notifications[index].copyWith(isRead: true);
      notifyListeners();

      // تحديث في قاعدة البيانات
      try {
        await SupabaseService.markNotificationAsRead(notificationId);
        debugPrint('✅ تم تحديث حالة الإشعار في قاعدة البيانات');
      } catch (e) {
        debugPrint('❌ خطأ في تحديث حالة الإشعار: $e');
        // في حالة الفشل، إرجاع الحالة المحلية
        _notifications[index] = _notifications[index].copyWith(isRead: false);
        notifyListeners();
      }
    }
  }

  void markAllAsRead() {
    _notifications = _notifications
        .map((n) => n.copyWith(isRead: true))
        .toList();
    notifyListeners();
  }

  void deleteNotification(String notificationId) {
    _notifications.removeWhere((n) => n.id == notificationId);
    notifyListeners();
  }

  void clearAllNotifications() {
    _notifications.clear();
    notifyListeners();
  }

  List<NotificationModel> getNotificationsByType(NotificationType type) {
    return _notifications.where((n) => n.type == type).toList();
  }

  List<NotificationModel> getUnreadNotifications() {
    return _notifications.where((n) => !n.isRead).toList();
  }

  List<NotificationModel> getRecentNotifications({int limit = 5}) {
    final sortedNotifications = List<NotificationModel>.from(_notifications);
    sortedNotifications.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    return sortedNotifications.take(limit).toList();
  }

  void addNotification(NotificationModel notification) {
    _notifications.insert(0, notification);
    notifyListeners();
  }

  // Simulate receiving a new notification
  void simulateNewNotification() {
    final newNotification = NotificationModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: 'إشعار جديد',
      body: 'هذا إشعار تجريبي جديد',
      type: NotificationType.general,
      createdAt: DateTime.now(),
      isRead: false,
    );

    addNotification(newNotification);
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
}
