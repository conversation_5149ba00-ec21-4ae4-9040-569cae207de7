import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/language_provider.dart';

class TeamDetailsScreen extends StatefulWidget {
  final Map<String, dynamic> team;

  const TeamDetailsScreen({super.key, required this.team});

  @override
  State<TeamDetailsScreen> createState() => _TeamDetailsScreenState();
}

class _TeamDetailsScreenState extends State<TeamDetailsScreen>
    with TickerProviderStateMixin {
  late TabController _mainTabController;
  late TabController _subTabController;

  // بيانات وهمية لمباريات الفريق
  final Map<String, List<Map<String, dynamic>>> teamMatches = {
    'خميس، 31 يوليو': [
      {
        'homeTeam': 'برشلونة',
        'awayTeam': 'سيفيلا',
        'homeTeamLogo': '🔴',
        'awayTeamLogo': '⚪',
        'time': '02:00 م',
        'competition': 'المباريات الودية الدولية',
        'date': '31 يوليو',
      },
    ],
    'الاثنين، 4 أغسطس': [
      {
        'homeTeam': 'برشلونة',
        'awayTeam': 'بايرن',
        'homeTeamLogo': '🔴',
        'awayTeamLogo': '🔴',
        'time': '02:00 م',
        'competition': 'المباريات الودية الدولية',
        'date': '4 أغسطس',
      },
    ],
    'الأحد، 10 أغسطس': [
      {
        'homeTeam': 'برشلونة',
        'awayTeam': 'كومو',
        'homeTeamLogo': '🔴',
        'awayTeamLogo': '🔵',
        'time': '01:00 م',
        'competition': 'كأس جوان غامبر',
        'date': '10 أغسطس',
      },
    ],
    'السبت، 16 أغسطس': [
      {
        'homeTeam': 'برشلونة',
        'awayTeam': 'ريال مايوركا',
        'homeTeamLogo': '🔴',
        'awayTeamLogo': '🟡',
        'time': '08:30 م',
        'competition': 'الدوري الإسباني',
        'date': '16 أغسطس',
      },
    ],
    'السبت، 23 أغسطس': [
      {
        'homeTeam': 'برشلونة',
        'awayTeam': 'ريال مدريد',
        'homeTeamLogo': '🔴',
        'awayTeamLogo': '⚪',
        'time': '08:30 م',
        'competition': 'الدوري الإسباني',
        'date': '23 أغسطس',
      },
    ],
  };

  @override
  void initState() {
    super.initState();
    _mainTabController = TabController(length: 3, vsync: this);
    _subTabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _mainTabController.dispose();
    _subTabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<LanguageProvider>(
      builder: (context, languageProvider, child) {
        return Scaffold(
          backgroundColor: Colors.black,
          appBar: AppBar(
            backgroundColor: Colors.black,
            elevation: 0,
            leading: IconButton(
              icon: Icon(Icons.arrow_back, color: Colors.white),
              onPressed: () => Navigator.pop(context),
            ),
            actions: [
              Container(
                margin: const EdgeInsets.only(right: 8),
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.grey[800],
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  '24',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              IconButton(
                icon: Icon(Icons.star_border, color: Colors.white),
                onPressed: () {},
              ),
              IconButton(
                icon: Icon(Icons.notifications_none, color: Colors.white),
                onPressed: () {},
              ),
            ],
          ),
          body: Column(
            children: [
              // شعار واسم الفريق
              Container(
                padding: const EdgeInsets.symmetric(vertical: 20),
                child: Column(
                  children: [
                    // شعار برشلونة الحقيقي
                    Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        color: Colors.transparent,
                        shape: BoxShape.circle,
                      ),
                      child: Stack(
                        children: [
                          // خلفية الشعار
                          Container(
                            width: 80,
                            height: 80,
                            decoration: BoxDecoration(
                              color: Color(0xFF004D98), // أزرق برشلونة
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: Color(0xFFDC143C), // أحمر برشلونة
                                width: 3,
                              ),
                            ),
                          ),
                          // شعار برشلونة
                          Center(
                            child: Container(
                              width: 60,
                              height: 60,
                              decoration: BoxDecoration(
                                color: Color(0xFF004D98),
                                shape: BoxShape.circle,
                              ),
                              child: Center(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Text(
                                      'FCB',
                                      style: TextStyle(
                                        color: Color(0xFFDC143C),
                                        fontSize: 12,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    Container(
                                      width: 30,
                                      height: 2,
                                      color: Color(0xFFDC143C),
                                    ),
                                    Text('⚽', style: TextStyle(fontSize: 16)),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 12),
                    // اسم الفريق
                    Text(
                      'برشلونة',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),

              // التبويبات الرئيسية
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 16),
                child: TabBar(
                  controller: _mainTabController,
                  indicator: const UnderlineTabIndicator(
                    borderSide: BorderSide(color: Color(0xFF007AFF), width: 3),
                    insets: EdgeInsets.symmetric(horizontal: 20),
                  ),
                  labelColor: Colors.white,
                  unselectedLabelColor: Colors.grey[400],
                  labelStyle: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                  indicatorSize: TabBarIndicatorSize.label,
                  dividerColor: Colors.transparent,
                  tabs: const [
                    Tab(text: 'تفاصيل'),
                    Tab(text: 'BUZZ'),
                    Tab(text: 'اللاعبون'),
                  ],
                ),
              ),

              const SizedBox(height: 20),

              // محتوى التبويبات الرئيسية
              Expanded(
                child: TabBarView(
                  controller: _mainTabController,
                  children: [
                    _buildDetailsTab(),
                    _buildBuzzTab(),
                    _buildPlayersTab(),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // تبويب التفاصيل
  Widget _buildDetailsTab() {
    return Column(
      children: [
        // التبويبات الفرعية مطابقة للصورة
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: Color(0xFF1C2833),
            borderRadius: BorderRadius.circular(25),
          ),
          child: TabBar(
            controller: _subTabController,
            indicator: BoxDecoration(
              color: Color(0xFF007AFF),
              borderRadius: BorderRadius.circular(20),
            ),
            labelColor: Colors.white,
            unselectedLabelColor: Colors.grey[400],
            labelStyle: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
            indicatorSize: TabBarIndicatorSize.tab,
            dividerColor: Colors.transparent,
            tabs: const [
              Tab(text: 'المباريات'),
              Tab(text: 'الترتيب'),
              Tab(text: 'إحصائيات'),
            ],
          ),
        ),

        const SizedBox(height: 20),

        // محتوى التبويبات الفرعية
        Expanded(
          child: TabBarView(
            controller: _subTabController,
            children: [
              _buildMatchesTab(),
              _buildStandingsTab(),
              _buildStatsTab(),
            ],
          ),
        ),
      ],
    );
  }

  // تبويب المباريات
  Widget _buildMatchesTab() {
    return Column(
      children: [
        // عنوان كل المباريات مطابق للصورة
        Container(
          width: double.infinity,
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: Color(0xFF2C3E50), // لون أغمق مطابق للصورة
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'كل المباريات',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Icon(
                Icons.keyboard_arrow_down,
                color: Colors.grey[400],
                size: 20,
              ),
            ],
          ),
        ),

        // قائمة المباريات
        Expanded(
          child: SingleChildScrollView(
            child: Column(
              children: teamMatches.entries.map((entry) {
                final date = entry.key;
                final matches = entry.value;

                return Column(
                  children: [
                    // عنوان التاريخ مطابق للصورة
                    Container(
                      width: double.infinity,
                      margin: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 8,
                      ),
                      decoration: BoxDecoration(
                        color: Color(0xFF007AFF),
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            date,
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          // أيقونة دائرية صغيرة
                          Container(
                            width: 16,
                            height: 16,
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.3),
                              shape: BoxShape.circle,
                            ),
                            child: Center(
                              child: Text(
                                '?',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                    // عنوان البطولة
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                      child: Text(
                        matches.first['competition'] ?? '',
                        style: TextStyle(
                          color: Colors.grey[400],
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),

                    // المباريات
                    ...matches.map((match) => _buildTeamMatchCard(match)),

                    const SizedBox(height: 16),
                  ],
                );
              }).toList(),
            ),
          ),
        ),
      ],
    );
  }

  // تبويب BUZZ
  Widget _buildBuzzTab() {
    return Center(
      child: Text(
        'BUZZ Content',
        style: TextStyle(color: Colors.white, fontSize: 18),
      ),
    );
  }

  // تبويب اللاعبون
  Widget _buildPlayersTab() {
    return Center(
      child: Text(
        'Players Content',
        style: TextStyle(color: Colors.white, fontSize: 18),
      ),
    );
  }

  // تبويب الترتيب
  Widget _buildStandingsTab() {
    return Center(
      child: Text(
        'Standings Content',
        style: TextStyle(color: Colors.white, fontSize: 18),
      ),
    );
  }

  // تبويب الإحصائيات
  Widget _buildStatsTab() {
    return Center(
      child: Text(
        'Stats Content',
        style: TextStyle(color: Colors.white, fontSize: 18),
      ),
    );
  }

  // بناء كارت مباراة الفريق مطابق للصورة
  Widget _buildTeamMatchCard(Map<String, dynamic> match) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Color(0xFF1C2833), // لون أغمق مطابق للصورة
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // الفريق الأول (برشلونة)
          Expanded(
            flex: 3,
            child: Row(
              children: [
                // شعار برشلونة
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: Color(0xFF004D98),
                    shape: BoxShape.circle,
                    border: Border.all(color: Color(0xFFDC143C), width: 1),
                  ),
                  child: Center(
                    child: Text(
                      'B',
                      style: TextStyle(
                        color: Color(0xFFDC143C),
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                // اسم الفريق
                Expanded(
                  child: Text(
                    'برشلونة',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // الوقت في المنتصف
          Expanded(
            flex: 2,
            child: Center(
              child: Text(
                match['time'] ?? '',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),

          // الفريق الثاني
          Expanded(
            flex: 3,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                // اسم الفريق الثاني
                Expanded(
                  child: Text(
                    match['awayTeam'] ?? '',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.end,
                  ),
                ),
                const SizedBox(width: 8),
                // شعار الفريق الثاني
                _buildOpponentLogo(match['awayTeam'] ?? ''),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // بناء شعار الفريق المنافس
  Widget _buildOpponentLogo(String teamName) {
    Color logoColor;
    String logoText;

    switch (teamName) {
      case 'سيفيلا':
        logoColor = Colors.red;
        logoText = 'S';
        break;
      case 'بايرن':
        logoColor = Colors.red;
        logoText = 'B';
        break;
      case 'كومو':
        logoColor = Colors.blue;
        logoText = 'C';
        break;
      case 'ريال مايوركا':
        logoColor = Colors.orange;
        logoText = 'M';
        break;
      case 'ريال مدريد':
        logoColor = Colors.white;
        logoText = 'R';
        break;
      default:
        logoColor = Colors.grey;
        logoText = '?';
    }

    return Container(
      width: 24,
      height: 24,
      decoration: BoxDecoration(color: logoColor, shape: BoxShape.circle),
      child: Center(
        child: Text(
          logoText,
          style: TextStyle(
            color: logoColor == Colors.white ? Colors.black : Colors.white,
            fontSize: 10,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }
}
