import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class FontSizeProvider extends ChangeNotifier {
  double _fontSize = 14.0; // Default font size
  
  double get fontSize => _fontSize;
  
  // Font size ranges
  static const double minFontSize = 12.0;
  static const double maxFontSize = 18.0;
  static const double defaultFontSize = 14.0;
  
  FontSizeProvider() {
    _loadFontSize();
  }
  
  Future<void> _loadFontSize() async {
    final prefs = await SharedPreferences.getInstance();
    _fontSize = prefs.getDouble('font_size') ?? defaultFontSize;
    notifyListeners();
  }
  
  Future<void> setFontSize(double size) async {
    if (size >= minFontSize && size <= maxFontSize) {
      _fontSize = size;
      
      final prefs = await SharedPreferences.getInstance();
      await prefs.setDouble('font_size', size);
      
      notifyListeners();
    }
  }
  
  // Get scaled text styles based on current font size
  TextStyle getScaledTextStyle(TextStyle? baseStyle) {
    if (baseStyle == null) return TextStyle(fontSize: _fontSize);
    
    final baseFontSize = baseStyle.fontSize ?? defaultFontSize;
    final scaleFactor = _fontSize / defaultFontSize;
    
    return baseStyle.copyWith(
      fontSize: baseFontSize * scaleFactor,
    );
  }
  
  // Get font size multiplier
  double get fontSizeMultiplier => _fontSize / defaultFontSize;
  
  // Reset to default
  Future<void> resetFontSize() async {
    await setFontSize(defaultFontSize);
  }
}
