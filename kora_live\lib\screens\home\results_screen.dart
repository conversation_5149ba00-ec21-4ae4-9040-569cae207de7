import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../constants/app_colors.dart';
import '../../providers/language_provider.dart';
import '../../utils/translation_extension.dart';
import '../../services/api_service.dart';
import '../details/match_details_screen.dart';

class ResultsScreen extends StatefulWidget {
  const ResultsScreen({super.key});

  @override
  State<ResultsScreen> createState() => _ResultsScreenState();
}

class _ResultsScreenState extends State<ResultsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  // بيانات وهمية للمباريات مقسمة حسب التاريخ
  final Map<String, List<Map<String, dynamic>>> matchesByDate = {
    'اليوم': [
      {
        'homeTeam': 'الحسين أربيد',
        'awayTeam': 'الوحدات',
        'homeTeamLogo': '🟡',
        'awayTeamLogo': '⚪',
        'time': '08:00 م',
        'competition': 'المجموعة 1',
        'isFavorite': false,
      },
      {
        'homeTeam': 'البنزرتي',
        'awayTeam': 'الأهلي',
        'homeTeamLogo': '🟠',
        'awayTeamLogo': '🔴',
        'time': '08:00 م',
        'competition': '',
        'isFavorite': false,
      },
    ],
    'غداً': [
      {
        'homeTeam': 'السيب',
        'awayTeam': 'الشباب العماني',
        'homeTeamLogo': '🟢',
        'awayTeamLogo': '🔵',
        'time': '06:30 م',
        'competition': '',
        'isFavorite': false,
      },
      {
        'homeTeam': 'فيتوريا غيماريش',
        'awayTeam': 'الاتحاد',
        'homeTeamLogo': '⚫',
        'awayTeamLogo': '🟡',
        'time': '08:00 م',
        'competition': '',
        'isFavorite': false,
      },
    ],
    'السبت، 26 يوليو': [
      {
        'homeTeam': 'السيب',
        'awayTeam': 'الشباب العماني',
        'homeTeamLogo': '🟢',
        'awayTeamLogo': '🔵',
        'time': '06:30 م',
        'competition': '',
        'isFavorite': false,
      },
      {
        'homeTeam': 'فيتوريا غيماريش',
        'awayTeam': 'الاتحاد',
        'homeTeamLogo': '⚫',
        'awayTeamLogo': '🟡',
        'time': '08:00 م',
        'competition': '',
        'isFavorite': false,
      },
    ],
    'الأحد، 27 يوليو': [
      {
        'homeTeam': 'ريال مدريد',
        'awayTeam': 'برشلونة',
        'homeTeamLogo': '⚪',
        'awayTeamLogo': '🔴',
        'time': '10:00 م',
        'competition': 'الكلاسيكو',
        'isFavorite': true,
      },
    ],
  };

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<LanguageProvider>(
      builder: (context, languageProvider, child) {
        return Container(
          color: Colors.black, // خلفية سوداء كما في الصورة
          child: Column(
            children: [
              const SizedBox(height: 20),

              // التبويبات العلوية
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 16),
                child: TabBar(
                  controller: _tabController,
                  indicator: const UnderlineTabIndicator(
                    borderSide: BorderSide(
                      color: Color(0xFF007AFF), // خط أزرق
                      width: 3,
                    ),
                    insets: EdgeInsets.symmetric(horizontal: 20),
                  ),
                  labelColor: Colors.white,
                  unselectedLabelColor: Colors.grey[400],
                  labelStyle: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                  unselectedLabelStyle: const TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 16,
                  ),
                  indicatorSize: TabBarIndicatorSize.label,
                  dividerColor: Colors.transparent,
                  tabs: const [
                    Tab(text: 'نتائجي'),
                    Tab(text: 'جميع النتائج'),
                    Tab(text: 'اليوم'),
                  ],
                ),
              ),

              const SizedBox(height: 20),

              // محتوى التبويبات
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildMyResults(),
                    _buildAllResults(),
                    _buildTodayResults(),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // تبويب نتائجي (فارغ مع رسالة)
  Widget _buildMyResults() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // رسالة للمستخدم
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            margin: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              color: Colors.grey[800],
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              children: [
                Text(
                  'الفرق التي تتابعها لا تلعب اليوم',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 10,
                  ),
                  decoration: BoxDecoration(
                    color: Color(0xFF007AFF),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    'شاهد جميع النتائج',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // تبويب جميع النتائج
  Widget _buildAllResults() {
    return SingleChildScrollView(
      child: Column(
        children: matchesByDate.entries.map((entry) {
          final date = entry.key;
          final matches = entry.value;

          return Column(
            children: [
              // عنوان التاريخ
              Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(vertical: 16),
                color: Colors.black,
                child: Column(
                  children: [
                    Text(
                      '— $date —',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${matches.length} مباريات',
                      style: TextStyle(color: Colors.grey[400], fontSize: 14),
                    ),
                  ],
                ),
              ),

              // أيقونات العمليات
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Icon(
                      Icons.delete_outline,
                      color: Colors.grey[600],
                      size: 24,
                    ),
                    Row(
                      children: [
                        Text(
                          'أبرز المباريات',
                          style: TextStyle(color: Colors.white, fontSize: 14),
                        ),
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: Colors.grey[700],
                            shape: BoxShape.circle,
                          ),
                          child: Text(
                            '365',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // المباريات
              ...matches.map((match) => _buildMatchCard(match)),

              const SizedBox(height: 20),
            ],
          );
        }).toList(),
      ),
    );
  }

  // تبويب اليوم
  Widget _buildTodayResults() {
    final todayMatches = matchesByDate['اليوم'] ?? [];

    return SingleChildScrollView(
      child: Column(
        children: [
          // عنوان اليوم
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 16),
            color: Colors.black,
            child: Column(
              children: [
                Text(
                  '— اليوم —',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${todayMatches.length} مباريات',
                  style: TextStyle(color: Colors.grey[400], fontSize: 14),
                ),
              ],
            ),
          ),

          // أيقونات العمليات
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Icon(Icons.delete_outline, color: Colors.grey[600], size: 24),
                Row(
                  children: [
                    Text(
                      'أبرز المباريات',
                      style: TextStyle(color: Colors.white, fontSize: 14),
                    ),
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        color: Colors.grey[700],
                        shape: BoxShape.circle,
                      ),
                      child: Text(
                        '365',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // المباريات
          ...todayMatches.map((match) => _buildMatchCard(match)),
        ],
      ),
    );
  }

  // بناء كارت المباراة
  Widget _buildMatchCard(Map<String, dynamic> match) {
    return GestureDetector(
      onTap: () {
        // الانتقال إلى صفحة تفاصيل المباراة
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => MatchDetailsScreen(match: match),
          ),
        );
      },
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.grey[800],
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            // الفريق الأول
            Expanded(
              child: Row(
                children: [
                  // شعار الفريق الأول
                  Container(
                    width: 30,
                    height: 30,
                    decoration: BoxDecoration(
                      color: Colors.grey[700],
                      shape: BoxShape.circle,
                    ),
                    child: Center(
                      child: Text(
                        match['homeTeamLogo'] ?? '⚽',
                        style: TextStyle(fontSize: 16),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  // اسم الفريق الأول
                  Expanded(
                    child: Text(
                      match['homeTeam'] ?? '',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // الوقت والمجموعة
            Column(
              children: [
                Text(
                  match['time'] ?? '',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (match['competition'] != null &&
                    match['competition'].isNotEmpty)
                  Text(
                    match['competition'],
                    style: TextStyle(color: Colors.grey[400], fontSize: 12),
                  ),
              ],
            ),

            // الفريق الثاني
            Expanded(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  // اسم الفريق الثاني
                  Expanded(
                    child: Text(
                      match['awayTeam'] ?? '',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                      textAlign: TextAlign.end,
                    ),
                  ),
                  const SizedBox(width: 12),
                  // شعار الفريق الثاني
                  Container(
                    width: 30,
                    height: 30,
                    decoration: BoxDecoration(
                      color: Colors.grey[700],
                      shape: BoxShape.circle,
                    ),
                    child: Center(
                      child: Text(
                        match['awayTeamLogo'] ?? '⚽',
                        style: TextStyle(fontSize: 16),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
