import 'package:flutter/foundation.dart';
import '../models/news_model.dart';
import '../utils/dummy_data.dart';

class NewsProvider extends ChangeNotifier {
  List<NewsModel> _allNews = [];
  List<NewsModel> _filteredNews = [];
  List<String> _categories = [];
  String _selectedCategory = 'الكل';
  bool _isLoading = false;
  String? _error;

  List<NewsModel> get allNews => _allNews;
  List<NewsModel> get filteredNews => _filteredNews;
  List<String> get categories => _categories;
  String get selectedCategory => _selectedCategory;
  bool get isLoading => _isLoading;
  String? get error => _error;

  NewsProvider() {
    loadNews();
  }

  Future<void> loadNews() async {
    _setLoading(true);
    _error = null;
    
    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));
      
      _allNews = DummyData.getDummyNews();
      _categories = DummyData.getNewsCategories();
      _filteredNews = _allNews;
      
      notifyListeners();
    } catch (e) {
      _error = 'حدث خطأ في تحميل الأخبار';
      debugPrint('Error loading news: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> refreshNews() async {
    await loadNews();
  }

  void filterByCategory(String category) {
    _selectedCategory = category;
    
    if (category == 'الكل') {
      _filteredNews = _allNews;
    } else {
      _filteredNews = _allNews.where((news) => news.category == category).toList();
    }
    
    notifyListeners();
  }

  void searchNews(String query) {
    if (query.isEmpty) {
      filterByCategory(_selectedCategory);
      return;
    }
    
    final searchQuery = query.toLowerCase();
    _filteredNews = _allNews.where((news) {
      return news.title.toLowerCase().contains(searchQuery) ||
             news.content.toLowerCase().contains(searchQuery) ||
             news.author.toLowerCase().contains(searchQuery) ||
             news.tags.any((tag) => tag.toLowerCase().contains(searchQuery));
    }).toList();
    
    notifyListeners();
  }

  List<NewsModel> getBreakingNews() {
    return _allNews.where((news) => news.isBreaking).toList();
  }

  List<NewsModel> getNewsByCategory(String category) {
    return _allNews.where((news) => news.category == category).toList();
  }

  List<NewsModel> getRecentNews({int limit = 5}) {
    final sortedNews = List<NewsModel>.from(_allNews);
    sortedNews.sort((a, b) => b.publishedAt.compareTo(a.publishedAt));
    return sortedNews.take(limit).toList();
  }

  NewsModel? getNewsById(String id) {
    try {
      return _allNews.firstWhere((news) => news.id == id);
    } catch (e) {
      return null;
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
}
