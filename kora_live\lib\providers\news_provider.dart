import 'package:flutter/foundation.dart';
import '../models/news_model.dart';
import '../utils/dummy_data.dart';
import '../services/supabase_service.dart';

class NewsProvider extends ChangeNotifier {
  List<NewsModel> _allNews = [];
  List<NewsModel> _filteredNews = [];
  List<String> _categories = [];
  String _selectedCategory = 'الكل';
  bool _isLoading = false;
  String? _error;

  List<NewsModel> get allNews => _allNews;
  List<NewsModel> get filteredNews => _filteredNews;
  List<String> get categories => _categories;
  String get selectedCategory => _selectedCategory;
  bool get isLoading => _isLoading;
  String? get error => _error;

  NewsProvider() {
    loadNews();
  }

  Future<void> loadNews() async {
    _setLoading(true);
    _error = null;

    try {
      debugPrint('🔄 تحميل الأخبار من Supabase...');

      // محاولة جلب الأخبار من Supabase
      final supabaseNews = await SupabaseService.getNews();

      if (supabaseNews.isNotEmpty) {
        // تحويل البيانات من Supabase إلى NewsModel
        _allNews = supabaseNews.map((newsData) {
          return NewsModel(
            id: newsData['id'].toString(),
            title: newsData['title'] ?? 'عنوان غير محدد',
            content: newsData['content'] ?? 'محتوى غير متوفر',
            imageUrl: newsData['image_url'],
            category: newsData['category'] ?? 'عام',
            publishedAt:
                DateTime.tryParse(newsData['created_at'] ?? '') ??
                DateTime.now(),
            author: newsData['author'] ?? 'مجهول',
            isBreaking: newsData['is_breaking'] ?? false,
            readTime: _calculateReadTime(newsData['content'] ?? ''),
            tags: List<String>.from(newsData['tags'] ?? []),
          );
        }).toList();

        debugPrint('✅ تم تحميل ${_allNews.length} خبر من Supabase');
      } else {
        // في حالة عدم وجود أخبار في قاعدة البيانات، استخدم البيانات التجريبية
        debugPrint(
          '⚠️ لا توجد أخبار في قاعدة البيانات، استخدام البيانات التجريبية',
        );
        _allNews = DummyData.getDummyNews();
      }

      // استخراج الفئات من الأخبار
      _categories = ['الكل', ..._allNews.map((news) => news.category).toSet()];
      _filteredNews = _allNews;

      notifyListeners();
    } catch (e) {
      _error = 'حدث خطأ في تحميل الأخبار';
      debugPrint('❌ خطأ في تحميل الأخبار: $e');

      // في حالة الخطأ، استخدم البيانات التجريبية
      _allNews = DummyData.getDummyNews();
      _categories = DummyData.getNewsCategories();
      _filteredNews = _allNews;
      notifyListeners();
    } finally {
      _setLoading(false);
    }
  }

  // حساب وقت القراءة التقريبي
  int _calculateReadTime(String content) {
    final wordCount = content.split(' ').length;
    return (wordCount / 200).ceil(); // متوسط 200 كلمة في الدقيقة
  }

  Future<void> refreshNews() async {
    await loadNews();
  }

  void filterByCategory(String category) {
    _selectedCategory = category;

    if (category == 'الكل') {
      _filteredNews = _allNews;
    } else {
      _filteredNews = _allNews
          .where((news) => news.category == category)
          .toList();
    }

    notifyListeners();
  }

  void searchNews(String query) {
    if (query.isEmpty) {
      filterByCategory(_selectedCategory);
      return;
    }

    final searchQuery = query.toLowerCase();
    _filteredNews = _allNews.where((news) {
      return news.title.toLowerCase().contains(searchQuery) ||
          news.content.toLowerCase().contains(searchQuery) ||
          news.author.toLowerCase().contains(searchQuery) ||
          news.tags.any((tag) => tag.toLowerCase().contains(searchQuery));
    }).toList();

    notifyListeners();
  }

  List<NewsModel> getBreakingNews() {
    return _allNews.where((news) => news.isBreaking).toList();
  }

  List<NewsModel> getNewsByCategory(String category) {
    return _allNews.where((news) => news.category == category).toList();
  }

  List<NewsModel> getRecentNews({int limit = 5}) {
    final sortedNews = List<NewsModel>.from(_allNews);
    sortedNews.sort((a, b) => b.publishedAt.compareTo(a.publishedAt));
    return sortedNews.take(limit).toList();
  }

  NewsModel? getNewsById(String id) {
    try {
      return _allNews.firstWhere((news) => news.id == id);
    } catch (e) {
      return null;
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
}
