import 'package:flutter/material.dart';

class AppLocalizations {
  final Locale locale;
  
  AppLocalizations(this.locale);
  
  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }
  
  static const LocalizationsDelegate<AppLocalizations> delegate = _AppLocalizationsDelegate();
  
  // Common
  String get appName => _localizedValues[locale.languageCode]?['app_name'] ?? 'كورة لايف';
  String get cancel => _localizedValues[locale.languageCode]?['cancel'] ?? 'إلغاء';
  String get confirm => _localizedValues[locale.languageCode]?['confirm'] ?? 'تأكيد';
  String get save => _localizedValues[locale.languageCode]?['save'] ?? 'حفظ';
  String get delete => _localizedValues[locale.languageCode]?['delete'] ?? 'حذف';
  String get reset => _localizedValues[locale.languageCode]?['reset'] ?? 'إعادة تعيين';
  String get settings => _localizedValues[locale.languageCode]?['settings'] ?? 'الإعدادات';
  
  // Navigation
  String get home => _localizedValues[locale.languageCode]?['home'] ?? 'الرئيسية';
  String get news => _localizedValues[locale.languageCode]?['news'] ?? 'أخبار';
  String get results => _localizedValues[locale.languageCode]?['results'] ?? 'نتائج';
  String get favorites => _localizedValues[locale.languageCode]?['favorites'] ?? 'اختياراتي';
  String get more => _localizedValues[locale.languageCode]?['more'] ?? 'المزيد';
  
  // Settings
  String get generalSettings => _localizedValues[locale.languageCode]?['general_settings'] ?? 'الإعدادات العامة';
  String get appearance => _localizedValues[locale.languageCode]?['appearance'] ?? 'المظهر والعرض';
  String get notifications => _localizedValues[locale.languageCode]?['notifications'] ?? 'الإشعارات';
  String get performance => _localizedValues[locale.languageCode]?['performance'] ?? 'الأداء والبيانات';
  String get resetSection => _localizedValues[locale.languageCode]?['reset_section'] ?? 'إعادة التعيين';
  
  // Theme
  String get theme => _localizedValues[locale.languageCode]?['theme'] ?? 'المظهر';
  String get unifiedMode => _localizedValues[locale.languageCode]?['unified_mode'] ?? 'الوضع الموحد';
  String get darkMode => _localizedValues[locale.languageCode]?['dark_mode'] ?? 'الوضع المظلم';
  
  // Language
  String get language => _localizedValues[locale.languageCode]?['language'] ?? 'اللغة';
  String get selectLanguage => _localizedValues[locale.languageCode]?['select_language'] ?? 'اختر اللغة';
  
  // Font Size
  String get fontSize => _localizedValues[locale.languageCode]?['font_size'] ?? 'حجم الخط';
  String get fontSizeDescription => _localizedValues[locale.languageCode]?['font_size_description'] ?? 'تخصيص حجم النصوص في التطبيق';
  String get small => _localizedValues[locale.languageCode]?['small'] ?? 'صغير';
  String get large => _localizedValues[locale.languageCode]?['large'] ?? 'كبير';
  String get preview => _localizedValues[locale.languageCode]?['preview'] ?? 'المعاينة';
  
  // Notifications
  String get enableNotifications => _localizedValues[locale.languageCode]?['enable_notifications'] ?? 'تفعيل الإشعارات';
  String get notificationsDescription => _localizedValues[locale.languageCode]?['notifications_description'] ?? 'تلقي إشعارات من التطبيق';
  String get matchNotifications => _localizedValues[locale.languageCode]?['match_notifications'] ?? 'إشعارات المباريات';
  String get matchNotificationsDescription => _localizedValues[locale.languageCode]?['match_notifications_description'] ?? 'تنبيهات بداية ونهاية المباريات';
  String get newsNotifications => _localizedValues[locale.languageCode]?['news_notifications'] ?? 'إشعارات الأخبار';
  String get newsNotificationsDescription => _localizedValues[locale.languageCode]?['news_notifications_description'] ?? 'تنبيهات الأخبار الرياضية الجديدة';
  String get favoriteTeamNotifications => _localizedValues[locale.languageCode]?['favorite_team_notifications'] ?? 'إشعارات الفرق المفضلة';
  String get favoriteTeamNotificationsDescription => _localizedValues[locale.languageCode]?['favorite_team_notifications_description'] ?? 'تنبيهات خاصة بفرقك المفضلة';
  
  // Performance
  String get autoRefresh => _localizedValues[locale.languageCode]?['auto_refresh'] ?? 'التحديث التلقائي';
  String get autoRefreshDescription => _localizedValues[locale.languageCode]?['auto_refresh_description'] ?? 'تحديث البيانات تلقائياً كل دقيقة';
  String get dataCompression => _localizedValues[locale.languageCode]?['data_compression'] ?? 'ضغط البيانات';
  String get dataCompressionDescription => _localizedValues[locale.languageCode]?['data_compression_description'] ?? 'توفير استهلاك الإنترنت';
  String get offlineMode => _localizedValues[locale.languageCode]?['offline_mode'] ?? 'الوضع غير المتصل';
  String get offlineModeDescription => _localizedValues[locale.languageCode]?['offline_mode_description'] ?? 'حفظ البيانات للعرض بدون إنترنت';
  
  // Reset
  String get resetSettings => _localizedValues[locale.languageCode]?['reset_settings'] ?? 'إعادة تعيين الإعدادات';
  String get resetSettingsDescription => _localizedValues[locale.languageCode]?['reset_settings_description'] ?? 'استعادة الإعدادات الافتراضية';
  String get clearData => _localizedValues[locale.languageCode]?['clear_data'] ?? 'مسح البيانات المحفوظة';
  String get clearDataDescription => _localizedValues[locale.languageCode]?['clear_data_description'] ?? 'حذف جميع البيانات المحلية';
  
  // Dialog messages
  String get resetConfirmTitle => _localizedValues[locale.languageCode]?['reset_confirm_title'] ?? 'إعادة تعيين الإعدادات';
  String get resetConfirmMessage => _localizedValues[locale.languageCode]?['reset_confirm_message'] ?? 'هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟';
  String get clearDataConfirmTitle => _localizedValues[locale.languageCode]?['clear_data_confirm_title'] ?? 'مسح البيانات';
  String get clearDataConfirmMessage => _localizedValues[locale.languageCode]?['clear_data_confirm_message'] ?? 'هل أنت متأكد من حذف جميع البيانات المحفوظة؟ لا يمكن التراجع عن هذا الإجراء.';
  
  // Success messages
  String get settingsResetSuccess => _localizedValues[locale.languageCode]?['settings_reset_success'] ?? 'تم إعادة تعيين الإعدادات بنجاح';
  String get dataClearedSuccess => _localizedValues[locale.languageCode]?['data_cleared_success'] ?? 'تم مسح البيانات بنجاح';
  
  // Auth
  String get login => _localizedValues[locale.languageCode]?['login'] ?? 'تسجيل الدخول';
  String get logout => _localizedValues[locale.languageCode]?['logout'] ?? 'تسجيل الخروج';
  String get welcome => _localizedValues[locale.languageCode]?['welcome'] ?? 'مرحباً بك';
  String get guestWelcome => _localizedValues[locale.languageCode]?['guest_welcome'] ?? 'مرحباً ضيفنا الكريم';
  
  static const Map<String, Map<String, String>> _localizedValues = {
    'ar': {
      'app_name': 'كورة لايف',
      'cancel': 'إلغاء',
      'confirm': 'تأكيد',
      'save': 'حفظ',
      'delete': 'حذف',
      'reset': 'إعادة تعيين',
      'settings': 'الإعدادات',
      'home': 'الرئيسية',
      'news': 'أخبار',
      'results': 'نتائج',
      'favorites': 'اختياراتي',
      'more': 'المزيد',
      'general_settings': 'الإعدادات العامة',
      'appearance': 'المظهر والعرض',
      'notifications': 'الإشعارات',
      'performance': 'الأداء والبيانات',
      'reset_section': 'إعادة التعيين',
      'theme': 'المظهر',
      'unified_mode': 'الوضع الموحد',
      'dark_mode': 'الوضع المظلم',
      'language': 'اللغة',
      'select_language': 'اختر اللغة',
      'font_size': 'حجم الخط',
      'font_size_description': 'تخصيص حجم النصوص في التطبيق',
      'small': 'صغير',
      'large': 'كبير',
      'preview': 'المعاينة',
      'enable_notifications': 'تفعيل الإشعارات',
      'notifications_description': 'تلقي إشعارات من التطبيق',
      'match_notifications': 'إشعارات المباريات',
      'match_notifications_description': 'تنبيهات بداية ونهاية المباريات',
      'news_notifications': 'إشعارات الأخبار',
      'news_notifications_description': 'تنبيهات الأخبار الرياضية الجديدة',
      'favorite_team_notifications': 'إشعارات الفرق المفضلة',
      'favorite_team_notifications_description': 'تنبيهات خاصة بفرقك المفضلة',
      'auto_refresh': 'التحديث التلقائي',
      'auto_refresh_description': 'تحديث البيانات تلقائياً كل دقيقة',
      'data_compression': 'ضغط البيانات',
      'data_compression_description': 'توفير استهلاك الإنترنت',
      'offline_mode': 'الوضع غير المتصل',
      'offline_mode_description': 'حفظ البيانات للعرض بدون إنترنت',
      'reset_settings': 'إعادة تعيين الإعدادات',
      'reset_settings_description': 'استعادة الإعدادات الافتراضية',
      'clear_data': 'مسح البيانات المحفوظة',
      'clear_data_description': 'حذف جميع البيانات المحلية',
      'reset_confirm_title': 'إعادة تعيين الإعدادات',
      'reset_confirm_message': 'هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟',
      'clear_data_confirm_title': 'مسح البيانات',
      'clear_data_confirm_message': 'هل أنت متأكد من حذف جميع البيانات المحفوظة؟ لا يمكن التراجع عن هذا الإجراء.',
      'settings_reset_success': 'تم إعادة تعيين الإعدادات بنجاح',
      'data_cleared_success': 'تم مسح البيانات بنجاح',
      'login': 'تسجيل الدخول',
      'logout': 'تسجيل الخروج',
      'welcome': 'مرحباً بك',
      'guest_welcome': 'مرحباً ضيفنا الكريم',
    },
    'en': {
      'app_name': 'Kora Live',
      'cancel': 'Cancel',
      'confirm': 'Confirm',
      'save': 'Save',
      'delete': 'Delete',
      'reset': 'Reset',
      'settings': 'Settings',
      'home': 'Home',
      'news': 'News',
      'results': 'Results',
      'favorites': 'Favorites',
      'more': 'More',
      'general_settings': 'General Settings',
      'appearance': 'Appearance & Display',
      'notifications': 'Notifications',
      'performance': 'Performance & Data',
      'reset_section': 'Reset',
      'theme': 'Theme',
      'unified_mode': 'Unified Mode',
      'dark_mode': 'Dark Mode',
      'language': 'Language',
      'select_language': 'Select Language',
      'font_size': 'Font Size',
      'font_size_description': 'Customize text size in the app',
      'small': 'Small',
      'large': 'Large',
      'preview': 'Preview',
      'enable_notifications': 'Enable Notifications',
      'notifications_description': 'Receive notifications from the app',
      'match_notifications': 'Match Notifications',
      'match_notifications_description': 'Alerts for match start and end',
      'news_notifications': 'News Notifications',
      'news_notifications_description': 'Alerts for new sports news',
      'favorite_team_notifications': 'Favorite Team Notifications',
      'favorite_team_notifications_description': 'Special alerts for your favorite teams',
      'auto_refresh': 'Auto Refresh',
      'auto_refresh_description': 'Automatically refresh data every minute',
      'data_compression': 'Data Compression',
      'data_compression_description': 'Save internet usage',
      'offline_mode': 'Offline Mode',
      'offline_mode_description': 'Save data for offline viewing',
      'reset_settings': 'Reset Settings',
      'reset_settings_description': 'Restore default settings',
      'clear_data': 'Clear Saved Data',
      'clear_data_description': 'Delete all local data',
      'reset_confirm_title': 'Reset Settings',
      'reset_confirm_message': 'Are you sure you want to reset all settings to default values?',
      'clear_data_confirm_title': 'Clear Data',
      'clear_data_confirm_message': 'Are you sure you want to delete all saved data? This action cannot be undone.',
      'settings_reset_success': 'Settings reset successfully',
      'data_cleared_success': 'Data cleared successfully',
      'login': 'Login',
      'logout': 'Logout',
      'welcome': 'Welcome',
      'guest_welcome': 'Welcome Dear Guest',
    },
    'fr': {
      'app_name': 'Kora Live',
      'cancel': 'Annuler',
      'confirm': 'Confirmer',
      'save': 'Enregistrer',
      'delete': 'Supprimer',
      'reset': 'Réinitialiser',
      'settings': 'Paramètres',
      'home': 'Accueil',
      'news': 'Actualités',
      'results': 'Résultats',
      'favorites': 'Favoris',
      'more': 'Plus',
      'general_settings': 'Paramètres Généraux',
      'appearance': 'Apparence et Affichage',
      'notifications': 'Notifications',
      'performance': 'Performance et Données',
      'reset_section': 'Réinitialisation',
      'theme': 'Thème',
      'unified_mode': 'Mode Unifié',
      'dark_mode': 'Mode Sombre',
      'language': 'Langue',
      'select_language': 'Sélectionner la Langue',
      'font_size': 'Taille de Police',
      'font_size_description': 'Personnaliser la taille du texte dans l\'app',
      'small': 'Petit',
      'large': 'Grand',
      'preview': 'Aperçu',
      'enable_notifications': 'Activer les Notifications',
      'notifications_description': 'Recevoir des notifications de l\'app',
      'match_notifications': 'Notifications de Match',
      'match_notifications_description': 'Alertes pour le début et la fin des matchs',
      'news_notifications': 'Notifications d\'Actualités',
      'news_notifications_description': 'Alertes pour les nouvelles sportives',
      'favorite_team_notifications': 'Notifications d\'Équipe Favorite',
      'favorite_team_notifications_description': 'Alertes spéciales pour vos équipes favorites',
      'auto_refresh': 'Actualisation Automatique',
      'auto_refresh_description': 'Actualiser automatiquement les données chaque minute',
      'data_compression': 'Compression des Données',
      'data_compression_description': 'Économiser l\'utilisation d\'internet',
      'offline_mode': 'Mode Hors Ligne',
      'offline_mode_description': 'Sauvegarder les données pour la consultation hors ligne',
      'reset_settings': 'Réinitialiser les Paramètres',
      'reset_settings_description': 'Restaurer les paramètres par défaut',
      'clear_data': 'Effacer les Données Sauvegardées',
      'clear_data_description': 'Supprimer toutes les données locales',
      'reset_confirm_title': 'Réinitialiser les Paramètres',
      'reset_confirm_message': 'Êtes-vous sûr de vouloir réinitialiser tous les paramètres aux valeurs par défaut?',
      'clear_data_confirm_title': 'Effacer les Données',
      'clear_data_confirm_message': 'Êtes-vous sûr de vouloir supprimer toutes les données sauvegardées? Cette action ne peut pas être annulée.',
      'settings_reset_success': 'Paramètres réinitialisés avec succès',
      'data_cleared_success': 'Données effacées avec succès',
      'login': 'Connexion',
      'logout': 'Déconnexion',
      'welcome': 'Bienvenue',
      'guest_welcome': 'Bienvenue Cher Invité',
    },
    'es': {
      'app_name': 'Kora Live',
      'cancel': 'Cancelar',
      'confirm': 'Confirmar',
      'save': 'Guardar',
      'delete': 'Eliminar',
      'reset': 'Restablecer',
      'settings': 'Configuración',
      'home': 'Inicio',
      'news': 'Noticias',
      'results': 'Resultados',
      'favorites': 'Favoritos',
      'more': 'Más',
      'general_settings': 'Configuración General',
      'appearance': 'Apariencia y Pantalla',
      'notifications': 'Notificaciones',
      'performance': 'Rendimiento y Datos',
      'reset_section': 'Restablecer',
      'theme': 'Tema',
      'unified_mode': 'Modo Unificado',
      'dark_mode': 'Modo Oscuro',
      'language': 'Idioma',
      'select_language': 'Seleccionar Idioma',
      'font_size': 'Tamaño de Fuente',
      'font_size_description': 'Personalizar el tamaño del texto en la app',
      'small': 'Pequeño',
      'large': 'Grande',
      'preview': 'Vista Previa',
      'enable_notifications': 'Habilitar Notificaciones',
      'notifications_description': 'Recibir notificaciones de la app',
      'match_notifications': 'Notificaciones de Partidos',
      'match_notifications_description': 'Alertas para el inicio y fin de partidos',
      'news_notifications': 'Notificaciones de Noticias',
      'news_notifications_description': 'Alertas para nuevas noticias deportivas',
      'favorite_team_notifications': 'Notificaciones de Equipo Favorito',
      'favorite_team_notifications_description': 'Alertas especiales para tus equipos favoritos',
      'auto_refresh': 'Actualización Automática',
      'auto_refresh_description': 'Actualizar datos automáticamente cada minuto',
      'data_compression': 'Compresión de Datos',
      'data_compression_description': 'Ahorrar uso de internet',
      'offline_mode': 'Modo Sin Conexión',
      'offline_mode_description': 'Guardar datos para visualización sin conexión',
      'reset_settings': 'Restablecer Configuración',
      'reset_settings_description': 'Restaurar configuración predeterminada',
      'clear_data': 'Borrar Datos Guardados',
      'clear_data_description': 'Eliminar todos los datos locales',
      'reset_confirm_title': 'Restablecer Configuración',
      'reset_confirm_message': '¿Estás seguro de que quieres restablecer toda la configuración a los valores predeterminados?',
      'clear_data_confirm_title': 'Borrar Datos',
      'clear_data_confirm_message': '¿Estás seguro de que quieres eliminar todos los datos guardados? Esta acción no se puede deshacer.',
      'settings_reset_success': 'Configuración restablecida exitosamente',
      'data_cleared_success': 'Datos borrados exitosamente',
      'login': 'Iniciar Sesión',
      'logout': 'Cerrar Sesión',
      'welcome': 'Bienvenido',
      'guest_welcome': 'Bienvenido Querido Invitado',
    },
  };
}

class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();
  
  @override
  bool isSupported(Locale locale) {
    return ['ar', 'en', 'fr', 'es'].contains(locale.languageCode);
  }
  
  @override
  Future<AppLocalizations> load(Locale locale) async {
    return AppLocalizations(locale);
  }
  
  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}
