import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_model.dart';
import '../constants/app_constants.dart';
import '../services/supabase_service.dart';

class AuthProvider extends ChangeNotifier {
  UserModel? _currentUser;
  bool _isLoggedIn = false;
  bool _isLoading = false;

  UserModel? get currentUser => _currentUser;
  bool get isLoggedIn => _isLoggedIn;
  bool get isLoading => _isLoading;

  AuthProvider() {
    _checkLoginStatus();
  }

  Future<void> _checkLoginStatus() async {
    _setLoading(true);

    try {
      final prefs = await SharedPreferences.getInstance();
      final isLoggedIn = prefs.getBool(AppConstants.isLoggedInKey) ?? false;
      final userEmail = prefs.getString(AppConstants.userEmailKey);

      if (isLoggedIn && userEmail != null) {
        _currentUser = UserModel(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          email: userEmail,
          createdAt: DateTime.now(),
          isEmailVerified: true,
        );
        _isLoggedIn = true;
      }
    } catch (e) {
      debugPrint('Error checking login status: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> login(String email, String password) async {
    _setLoading(true);

    try {
      debugPrint('🔐 محاولة تسجيل الدخول: $email');
      final response = await SupabaseService.signIn(
        email: email,
        password: password,
      );

      debugPrint('📝 استجابة تسجيل الدخول: ${response.user?.id}');

      if (response.user != null) {
        // التحقق من تأكيد البريد الإلكتروني
        if (response.user!.emailConfirmedAt == null) {
          debugPrint('❌ البريد الإلكتروني غير مؤكد');
          // إرجاع خطأ خاص بعدم تأكيد البريد
          throw Exception('يرجى تأكيد بريدك الإلكتروني أولاً قبل تسجيل الدخول');
        }

        _currentUser = UserModel(
          id: response.user!.id,
          email: response.user!.email ?? email,
          createdAt: DateTime.parse(response.user!.createdAt),
          lastLoginAt: DateTime.now(),
          isEmailVerified: response.user!.emailConfirmedAt != null,
        );

        _isLoggedIn = true;

        // Save login state
        final prefs = await SharedPreferences.getInstance();
        await prefs.setBool(AppConstants.isLoggedInKey, true);
        await prefs.setString(AppConstants.userEmailKey, email);

        debugPrint('✅ تم تسجيل الدخول بنجاح');
        notifyListeners();
        return true;
      }

      debugPrint('❌ فشل تسجيل الدخول: لا يوجد مستخدم');
      return false;
    } catch (e) {
      debugPrint('❌ خطأ في تسجيل الدخول: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> signUp(String email, String password) async {
    _setLoading(true);

    try {
      debugPrint('📝 محاولة إنشاء حساب جديد: $email');

      // أولاً، تحقق من وجود الإيميل مسبقاً عن طريق محاولة تسجيل الدخول
      try {
        final loginResponse = await SupabaseService.signIn(
          email: email,
          password: password,
        );

        if (loginResponse.user != null) {
          debugPrint(
            '⚠️ الإيميل موجود مسبقاً - يرجى تسجيل الدخول بدلاً من إنشاء حساب جديد',
          );
          return false;
        }
      } catch (e) {
        // إذا فشل تسجيل الدخول، فهذا يعني أن الحساب غير موجود أو كلمة المرور خاطئة
        // يمكننا المتابعة لإنشاء الحساب
        debugPrint('🔍 الإيميل غير موجود مسبقاً، يمكن إنشاء حساب جديد');
      }

      final response = await SupabaseService.signUp(
        email: email,
        password: password,
      );

      debugPrint('📝 استجابة إنشاء الحساب: ${response.user?.id}');
      debugPrint(
        '📧 حالة البريد الإلكتروني: ${response.user?.emailConfirmedAt}',
      );

      if (response.user != null) {
        // لا نقوم بتسجيل الدخول التلقائي - فقط إنشاء الحساب
        debugPrint('✅ تم إنشاء الحساب بنجاح - يتطلب تأكيد البريد الإلكتروني');
        debugPrint('📧 حالة التأكيد: ${response.user!.emailConfirmedAt}');

        // لا نحفظ حالة تسجيل الدخول هنا
        // المستخدم يحتاج للتحقق من بريده الإلكتروني أولاً

        return true;
      }

      debugPrint('❌ فشل إنشاء الحساب: لا يوجد مستخدم');
      return false;
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء الحساب: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> resetPassword(String email) async {
    _setLoading(true);

    try {
      await SupabaseService.resetPassword(email: email);
      return true;
    } catch (e) {
      debugPrint('Reset password error: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<void> logout() async {
    _setLoading(true);

    try {
      // Sign out from Supabase
      await SupabaseService.signOut();

      // Clear saved login state
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(AppConstants.isLoggedInKey);
      await prefs.remove(AppConstants.userEmailKey);

      _currentUser = null;
      _isLoggedIn = false;

      notifyListeners();
    } catch (e) {
      debugPrint('Logout error: $e');
    } finally {
      _setLoading(false);
    }
  }

  // دالة لتنظيف حالة المصادقة عند الدخول كضيف
  Future<void> clearAuthState() async {
    try {
      // Clear saved login state
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(AppConstants.isLoggedInKey);
      await prefs.remove(AppConstants.userEmailKey);

      _currentUser = null;
      _isLoggedIn = false;

      notifyListeners();
      debugPrint('🧹 تم تنظيف حالة المصادقة للضيف');
    } catch (e) {
      debugPrint('خطأ في تنظيف حالة المصادقة: $e');
    }
  }

  Future<bool> updateProfile({
    String? name,
    String? profileImageUrl,
    List<String>? favoriteTeams,
  }) async {
    if (_currentUser == null) return false;

    _setLoading(true);

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));

      _currentUser = _currentUser!.copyWith(
        name: name,
        profileImageUrl: profileImageUrl,
        favoriteTeams: favoriteTeams,
      );

      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('Update profile error: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
}
