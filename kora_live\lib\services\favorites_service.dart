import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

class FavoritesService {
  static const String _favTeamsKey = 'favorite_teams';
  static const String _favMatchesKey = 'favorite_matches';
  static const String _favNewsKey = 'favorite_news';

  /// إضافة فريق للمفضلة
  static Future<bool> addFavoriteTeam(Map<String, dynamic> team) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final favoriteTeams = await getFavoriteTeams();

      // التحقق من عدم وجود الفريق مسبقاً
      final exists = favoriteTeams.any((t) => t['id'] == team['id']);
      if (exists) return false;

      favoriteTeams.add(team);
      final jsonString = jsonEncode(favoriteTeams);
      await prefs.setString(_favTeamsKey, jsonString);
      return true;
    } catch (e) {
      debugPrint('خطأ في إضافة الفريق للمفضلة: $e');
      return false;
    }
  }

  /// إزالة فريق من المفضلة
  static Future<bool> removeFavoriteTeam(String teamId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final favoriteTeams = await getFavoriteTeams();

      favoriteTeams.removeWhere((team) => team['id'] == teamId);
      final jsonString = jsonEncode(favoriteTeams);
      await prefs.setString(_favTeamsKey, jsonString);
      return true;
    } catch (e) {
      debugPrint('خطأ في إزالة الفريق من المفضلة: $e');
      return false;
    }
  }

  /// جلب الفرق المفضلة
  static Future<List<Map<String, dynamic>>> getFavoriteTeams() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(_favTeamsKey);
      if (jsonString == null) return [];

      final List<dynamic> jsonList = jsonDecode(jsonString);
      return jsonList.cast<Map<String, dynamic>>();
    } catch (e) {
      debugPrint('خطأ في جلب الفرق المفضلة: $e');
      return [];
    }
  }

  /// التحقق من كون الفريق مفضل
  static Future<bool> isTeamFavorite(String teamId) async {
    final favoriteTeams = await getFavoriteTeams();
    return favoriteTeams.any((team) => team['id'] == teamId);
  }

  /// إضافة مباراة للمفضلة
  static Future<bool> addFavoriteMatch(Map<String, dynamic> match) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final favoriteMatches = await getFavoriteMatches();

      final exists = favoriteMatches.any((m) => m['id'] == match['id']);
      if (exists) return false;

      favoriteMatches.add(match);
      final jsonString = jsonEncode(favoriteMatches);
      await prefs.setString(_favMatchesKey, jsonString);
      return true;
    } catch (e) {
      debugPrint('خطأ في إضافة المباراة للمفضلة: $e');
      return false;
    }
  }

  /// إزالة مباراة من المفضلة
  static Future<bool> removeFavoriteMatch(String matchId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final favoriteMatches = await getFavoriteMatches();

      favoriteMatches.removeWhere((match) => match['id'] == matchId);
      final jsonString = jsonEncode(favoriteMatches);
      await prefs.setString(_favMatchesKey, jsonString);
      return true;
    } catch (e) {
      debugPrint('خطأ في إزالة المباراة من المفضلة: $e');
      return false;
    }
  }

  /// جلب المباريات المفضلة
  static Future<List<Map<String, dynamic>>> getFavoriteMatches() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(_favMatchesKey);
      if (jsonString == null) return [];

      final List<dynamic> jsonList = jsonDecode(jsonString);
      return jsonList.cast<Map<String, dynamic>>();
    } catch (e) {
      debugPrint('خطأ في جلب المباريات المفضلة: $e');
      return [];
    }
  }

  /// التحقق من كون المباراة مفضلة
  static Future<bool> isMatchFavorite(String matchId) async {
    final favoriteMatches = await getFavoriteMatches();
    return favoriteMatches.any((match) => match['id'] == matchId);
  }

  /// إضافة خبر للمفضلة
  static Future<bool> addFavoriteNews(Map<String, dynamic> news) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final favoriteNews = await getFavoriteNews();

      final exists = favoriteNews.any((n) => n['id'] == news['id']);
      if (exists) return false;

      favoriteNews.add(news);
      final jsonString = jsonEncode(favoriteNews);
      await prefs.setString(_favNewsKey, jsonString);
      return true;
    } catch (e) {
      debugPrint('خطأ في إضافة الخبر للمفضلة: $e');
      return false;
    }
  }

  /// إزالة خبر من المفضلة
  static Future<bool> removeFavoriteNews(String newsId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final favoriteNews = await getFavoriteNews();

      favoriteNews.removeWhere((news) => news['id'] == newsId);
      final jsonString = jsonEncode(favoriteNews);
      await prefs.setString(_favNewsKey, jsonString);
      return true;
    } catch (e) {
      debugPrint('خطأ في إزالة الخبر من المفضلة: $e');
      return false;
    }
  }

  /// جلب الأخبار المفضلة
  static Future<List<Map<String, dynamic>>> getFavoriteNews() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(_favNewsKey);
      if (jsonString == null) return [];

      final List<dynamic> jsonList = jsonDecode(jsonString);
      return jsonList.cast<Map<String, dynamic>>();
    } catch (e) {
      debugPrint('خطأ في جلب الأخبار المفضلة: $e');
      return [];
    }
  }

  /// التحقق من كون الخبر مفضل
  static Future<bool> isNewsFavorite(String newsId) async {
    final favoriteNews = await getFavoriteNews();
    return favoriteNews.any((news) => news['id'] == newsId);
  }

  /// مسح جميع المفضلات
  static Future<bool> clearAllFavorites() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_favTeamsKey);
      await prefs.remove(_favMatchesKey);
      await prefs.remove(_favNewsKey);
      return true;
    } catch (e) {
      debugPrint('خطأ في مسح المفضلات: $e');
      return false;
    }
  }

  /// جلب إحصائيات المفضلة
  static Future<Map<String, int>> getFavoritesStats() async {
    final teams = await getFavoriteTeams();
    final matches = await getFavoriteMatches();
    final news = await getFavoriteNews();

    return {
      'teams': teams.length,
      'matches': matches.length,
      'news': news.length,
      'total': teams.length + matches.length + news.length,
    };
  }
}
