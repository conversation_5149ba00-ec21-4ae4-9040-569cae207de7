{"extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": {"account_extension_type": 0, "active_permissions": {"api": ["management", "system.display", "system.storage", "webstorePrivate", "system.cpu", "system.memory", "system.network"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "app_launcher_ordinal": "t", "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://chrome.google.com/webstore"}, "urls": ["https://chrome.google.com/webstore"]}, "description": "Discover great apps, games, extensions and themes for Google Chrome.", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtl3tO0osjuzRsf6xtD2SKxPlTfuoy7AWoObysitBPvH5fE1NaAA1/2JkPWkVDhdLBWLaIBPYeXbzlHp3y4Vv/4XG+aN5qFE3z+1RU/NqkzVYHtIpVScf3DjTYtKVL66mzVGijSoAIwbFCC3LpGdaoe6Q1rSRDp76wR6jjFzsYwQIDAQAB", "name": "Web Store", "permissions": ["webstorePrivate", "management", "system.cpu", "system.display", "system.memory", "system.network", "system.storage"], "version": "0.2"}, "needs_sync": true, "page_ordinal": "n", "path": "C:\\Program Files\\Google\\Chrome\\Application\\138.0.7204.169\\resources\\web_store", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "fignfifoniblkonapihmkfakmlgkbkcf": {"account_extension_type": 0, "active_permissions": {"api": ["metricsPrivate", "systemPrivate", "ttsEngine", "offscreen"], "explicit_host": ["https://www.google.com/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"service_worker": "service_worker.js"}, "description": "Component extension providing speech via the Google network text-to-speech service.", "host_permissions": ["https://www.google.com/"], "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA5mnqF6oM8Q5tYd7YqL40YL7Keftt4PwydehlNOyNlCiWDM/7SiQYwxYvVHMj1i03z7B5lZXQinrcqhHhoIgcSHK1JrdzVSJxPRVdmV0rJLv0KQgmVwL8p8MfN6SmHs+72xz+1GoRWpd0WlHMil7RzGKJA4Ku+9jxxsXoxes9eeV1hCavkb1dSF+mlQbaNiw7u1hhvc5mmeuEcWjoce8r8B2R4wmnGbuTLfoSchZ6jkasynmOaFxyT4jiYDYgrNtWRTQ/9PuPduJ+uBWVT/o2ZhDK2XcywVwzUfYIXDLDblK+YdZi8w8ZBNvc7hP9/iZr6/eoUpfsLa8qlJgyLBQebwIDAQAB", "manifest_version": 3, "name": "Google Network Speech", "permissions": ["metricsPrivate", "offscreen", "systemPrivate", "ttsEngine"], "tts_engine": {"voices": [{"event_types": ["start", "end", "error"], "gender": "female", "lang": "de-DE", "remote": true, "voice_name": "Google Deutsch"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "en-US", "remote": true, "voice_name": "Google US English"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "en-GB", "remote": true, "voice_name": "Google UK English Female"}, {"event_types": ["start", "end", "error"], "gender": "male", "lang": "en-GB", "remote": true, "voice_name": "Google UK English Male"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "es-ES", "remote": true, "voice_name": "Google español"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "es-US", "remote": true, "voice_name": "Google español de Estados Unidos"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "fr-FR", "remote": true, "voice_name": "Google français"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "hi-IN", "remote": true, "voice_name": "Google हिन्दी"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "id-ID", "remote": true, "voice_name": "Google Bahasa Indonesia"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "it-IT", "remote": true, "voice_name": "Google italiano"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ja-<PERSON>", "remote": true, "voice_name": "Google 日本語"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ko-KR", "remote": true, "voice_name": "Google 한국의"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "nl-NL", "remote": true, "voice_name": "Google Nederlands"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "pl-PL", "remote": true, "voice_name": "Google polski"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "pt-BR", "remote": true, "voice_name": "Google português do Brasil"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ru-RU", "remote": true, "voice_name": "Google русский"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-CN", "remote": true, "voice_name": "Google 普通话（中国大陆）"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-HK", "remote": true, "voice_name": "Google 粤語（香港）"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-TW", "remote": true, "voice_name": "Google 國語（臺灣）"}]}, "version": "1.0"}, "path": "C:\\Program Files\\Google\\Chrome\\Application\\138.0.7204.169\\resources\\network_speech_synthesis/mv3", "preferences": {}, "regular_only_preferences": {}, "service_worker_registration_info": {"version": "1.0"}, "serviceworkerevents": ["ttsEngine.onPause", "ttsEngine.onResume", "ttsEngine.onSpeak", "ttsEngine.onStop"], "was_installed_by_default": false, "was_installed_by_oem": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate"], "explicit_host": ["chrome://resources/*", "chrome://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "index.html", "name": "Chrome PDF Viewer", "offline_enabled": true, "permissions": ["chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write"]}], "version": "1"}, "path": "C:\\Program Files\\Google\\Chrome\\Application\\138.0.7204.169\\resources\\pdf", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "nkeimhogjdpnpccoofpliimaahmaaome": {"account_extension_type": 0, "active_permissions": {"api": ["processes", "webrtcLoggingPrivate", "system.cpu", "enterprise.hardwarePlatform"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": ["runtime.onConnectExternal"], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"page": "background.html", "persistent": false}, "externally_connectable": {"ids": ["moklfjoegmpoolceggbebbmgbddlhdgp", "ldmpofkllgeicjiihkimgeccbhghhmfj", "denipklgekfpcdmbahmbpnmokgajnhma", "kjfhgcncjdebkoofmbjoiemiboifnpbo", "ikfcpmgefdpheiiomgmhlmmkihchmdlj", "jlgegmdnodfhciolbdjciihnlaljdbjo", "lkbhffjfgpmpeppncnimiiikojibkhnm", "acdafoiapclbpdkhnighhilgampkglpc", "hkamnlhnogggfddmjomgbdokdkgfelgg"], "matches": ["https://*.meet.google.com/*"]}, "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDAQt2ZDdPfoSe/JI6ID5bgLHRCnCu9T36aYczmhw/tnv6QZB2I6WnOCMZXJZlRdqWc7w9jo4BWhYS50Vb4weMfh/I0On7VcRwJUgfAxW2cHB+EkmtI1v4v/OU24OqIa1Nmv9uRVeX0GjhQukdLNhAE6ACWooaf5kqKlCeK+1GOkQIDAQAB", "manifest_version": 2, "name": "Google Hangouts", "permissions": ["enterprise.hardwarePlatform", "processes", "system.cpu", "webrtcLoggingPrivate"], "version": "1.3.24"}, "path": "C:\\Program Files\\Google\\Chrome\\Application\\138.0.7204.169\\resources\\hangout_services", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}}}, "pinned_tabs": [], "protection": {"macs": {"account_values": {"browser": {"show_home_button": "5759BFC3DD8574A280728AD1E16B84B48624D3E0C5AA6685B6B354CFCC3383EF"}, "extensions": {"ui": {"developer_mode": "69AD1462CB5BC65D9D57724C6DC956F71ECA99ECEE367CFBC904F3BFB1267F3B"}}, "homepage": "F428B391E1A1A6B9DB147B0B2CEF397C4A6AC781212315C2E213F7D87BBC7731", "homepage_is_newtabpage": "A248080B4DDD6FC237C2304EE9B4D601E6898614B5E49416B77A57F66A1B4ED1", "session": {"restore_on_startup": "064CD150FC5DA45CC8DEF93F12D47A772F7839ACD6AB612A68082E57CE35718C", "startup_urls": "0B265376D2DF1EC6E44F1A6E85EDF7EF496A1714CCF672D31247329D73F0BE0A"}}, "browser": {"show_home_button": "9667547DD49817D35C99634E187332518E5D5B0A0C678C84C352923BAA0A2FE5"}, "default_search_provider_data": {"template_url_data": "5B3CEE40F264F66ACB8D9AAFB68E2B06E2225D2E790343E437CF3E0B7A30AA98"}, "enterprise_signin": {"policy_recovery_token": "52F6905A24E7F49E70879813D89145731B3C08EB63EEA8729D804FAF9916BB06"}, "extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": "C235B69574F20DD909BFD364D16EEA849422D23FEB8B8FB8FA1553271F7C1757", "fignfifoniblkonapihmkfakmlgkbkcf": "927FAFF64CF5A6814F84818FE5F624D6BF18ACB3785B6E739E03271709DDD08F", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "42C7367D9B3FF8F354768A441832A069D9A80758A4E5553CCC7F645D04D367FB", "nkeimhogjdpnpccoofpliimaahmaaome": "8C1700690F1B5BBEAAA84F97C8C4C1D0DA6DA6A995889187E49375618C5855A8"}, "ui": {"developer_mode": "398837823F2369E8907D5F6535EAB55297D41661BD4B89E997242BE9C91B625C"}}, "google": {"services": {"account_id": "ACBFC9589CAF380ACDC56EC40B01D1E5395A9DE62CFBF41AB93A256CA8D49756", "last_signed_in_username": "CAD8C5D93AD56482CACC3B77095664DF2971F7F05EC212CEE0932AFEF3742CC7", "last_username": "E651DFD6EA7B47AC2B7F1DDBB0C6546403D635E8013D2D57723DB088A6D5E019"}}, "homepage": "92F996F21DD597281B450B839D64050E0B3C3E2F90537447B3738DBCC2A1C99D", "homepage_is_newtabpage": "DCB2945E0D7CF8E6E41C29371297C4B9D86E72DF45A91DD00D16D88007B0EC77", "media": {"cdm": {"origin_data": "5E6CA96EF8FCFA29324BA1EF30B979450015AAD098BB610B6F1B0C293F9BC4D3"}, "storage_id_salt": "8C49E3E5429E2CBFB6B2A1EFA29E20B2F62D530F1ACAFEE11C29C3640AB07864"}, "module_blocklist_cache_md5_digest": "8D53D7715FAF72560AE88425CF7DAD57728CF49E1CE0A4859A27D4BCD198961A", "pinned_tabs": "0E11F243F2C1C5391C602C5FD0501F89975C59F2048CC1DA34BDA63C0BD8A68F", "prefs": {"preference_reset_time": "1C148D535D03AB176D1029A68A333393D97AEBBA20FBB301625DABC78988236B"}, "safebrowsing": {"incidents_sent": "838F01B4E9642451469BB83A8955636565B39DAC341A2654FB22180748E90FC5"}, "search_provider_overrides": "320CD7DE6D8FEE597A69A1CA414A2AF8FE4AB02B6CA0AF5443CA97ECCC5FF9C3", "session": {"restore_on_startup": "371211DF498405A6F2CE3E2F69006BC8BD40ADF909A7B1FA827C0195EA499D2B", "startup_urls": "61DD4DADFBFD5ECA4E56CECA92F2D11B998FFF1C2BC55C87162A0308EF9FFAE5"}}, "super_mac": "AC3A75E13776D230A06BFC2FEA15C0907140BCA0C0E8DFFEACEA33503157F081"}}