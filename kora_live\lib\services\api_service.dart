// سيتم استخدام هذه الـ imports لاحقاً عند ربط API حقيقي
// import 'dart:convert';
// import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';

class ApiService {
  // سيتم استخدام هذه المتغيرات لاحقاً عند ربط API حقيقي
  // static const String _baseUrl = 'https://api.football-data.org/v4';
  // static const String _apiKey = 'YOUR_API_KEY';
  // static const String _freeApiUrl = 'https://api.openligadb.de/getmatchdata';

  /// جلب المباريات الحالية
  static Future<List<Map<String, dynamic>>> getCurrentMatches() async {
    try {
      // استخدام بيانات تجريبية حتى نحصل على API key
      return _getMockMatches();
    } catch (e) {
      debugPrint('خطأ في جلب المباريات: $e');
      return _getMockMatches();
    }
  }

  /// جلب نتائج المباريات المنتهية
  static Future<List<Map<String, dynamic>>> getFinishedMatches() async {
    try {
      return _getMockFinishedMatches();
    } catch (e) {
      debugPrint('خطأ في جلب النتائج: $e');
      return _getMockFinishedMatches();
    }
  }

  /// جلب المباريات القادمة
  static Future<List<Map<String, dynamic>>> getUpcomingMatches() async {
    try {
      return _getMockUpcomingMatches();
    } catch (e) {
      debugPrint('خطأ في جلب المباريات القادمة: $e');
      return _getMockUpcomingMatches();
    }
  }

  /// جلب الأخبار الرياضية
  static Future<List<Map<String, dynamic>>> getSportsNews() async {
    try {
      return _getMockNews();
    } catch (e) {
      debugPrint('خطأ في جلب الأخبار: $e');
      return _getMockNews();
    }
  }

  /// جلب ترتيب الدوريات
  static Future<List<Map<String, dynamic>>> getLeagueStandings(
    String leagueId,
  ) async {
    try {
      return _getMockStandings();
    } catch (e) {
      debugPrint('خطأ في جلب الترتيب: $e');
      return _getMockStandings();
    }
  }

  /// بيانات تجريبية للمباريات الحالية
  static List<Map<String, dynamic>> _getMockMatches() {
    return [
      {
        'id': '1',
        'homeTeam': 'الأهلي',
        'awayTeam': 'الزمالك',
        'homeScore': 1,
        'awayScore': 1,
        'status': 'LIVE',
        'minute': 67,
        'competition': 'الدوري المصري',
        'date': DateTime.now().toIso8601String(),
        'homeLogo': '🔴',
        'awayLogo': '⚪',
      },
      {
        'id': '2',
        'homeTeam': 'ليفربول',
        'awayTeam': 'مانشستر سيتي',
        'homeScore': 2,
        'awayScore': 1,
        'status': 'LIVE',
        'minute': 78,
        'competition': 'الدوري الإنجليزي',
        'date': DateTime.now().toIso8601String(),
        'homeLogo': '🔴',
        'awayLogo': '🔵',
      },
    ];
  }

  /// بيانات تجريبية للمباريات المنتهية
  static List<Map<String, dynamic>> _getMockFinishedMatches() {
    return [
      {
        'id': '3',
        'homeTeam': 'ريال مدريد',
        'awayTeam': 'برشلونة',
        'homeScore': 2,
        'awayScore': 1,
        'status': 'FINISHED',
        'competition': 'الكلاسيكو',
        'date': DateTime.now()
            .subtract(const Duration(days: 1))
            .toIso8601String(),
        'homeLogo': '👑',
        'awayLogo': '🔵',
      },
      {
        'id': '4',
        'homeTeam': 'بايرن ميونخ',
        'awayTeam': 'دورتموند',
        'homeScore': 3,
        'awayScore': 0,
        'status': 'FINISHED',
        'competition': 'البوندسليجا',
        'date': DateTime.now()
            .subtract(const Duration(days: 2))
            .toIso8601String(),
        'homeLogo': '🔴',
        'awayLogo': '🟡',
      },
    ];
  }

  /// بيانات تجريبية للمباريات القادمة
  static List<Map<String, dynamic>> _getMockUpcomingMatches() {
    return [
      {
        'id': '5',
        'homeTeam': 'الإسماعيلي',
        'awayTeam': 'بيراميدز',
        'status': 'SCHEDULED',
        'competition': 'الدوري المصري',
        'date': DateTime.now().add(const Duration(days: 1)).toIso8601String(),
        'time': '20:00',
        'homeLogo': '🟡',
        'awayLogo': '🔺',
      },
      {
        'id': '6',
        'homeTeam': 'تشيلسي',
        'awayTeam': 'أرسنال',
        'status': 'SCHEDULED',
        'competition': 'الدوري الإنجليزي',
        'date': DateTime.now().add(const Duration(days: 2)).toIso8601String(),
        'time': '17:30',
        'homeLogo': '🔵',
        'awayLogo': '🔴',
      },
    ];
  }

  /// بيانات تجريبية للأخبار
  static List<Map<String, dynamic>> _getMockNews() {
    return [
      {
        'id': '1',
        'title': 'الأهلي يتعاقد مع لاعب جديد',
        'summary': 'النادي الأهلي يعلن عن ضم لاعب وسط جديد للفريق الأول',
        'content':
            'أعلن النادي الأهلي رسمياً عن التعاقد مع لاعب الوسط الجديد...',
        'category': 'الدوري المصري',
        'publishedAt': DateTime.now()
            .subtract(const Duration(hours: 2))
            .toIso8601String(),
        'imageUrl': 'https://via.placeholder.com/300x200',
        'isBreaking': true,
        'views': 1250,
        'comments': 45,
      },
      {
        'id': '2',
        'title': 'ليفربول يفوز في مباراة مثيرة',
        'summary': 'فوز مستحق لليفربول على مانشستر سيتي بنتيجة 3-2',
        'content': 'حقق ليفربول فوزاً مثيراً على مانشستر سيتي في مباراة...',
        'category': 'الدوري الإنجليزي',
        'publishedAt': DateTime.now()
            .subtract(const Duration(hours: 5))
            .toIso8601String(),
        'imageUrl': 'https://via.placeholder.com/300x200',
        'isBreaking': false,
        'views': 2100,
        'comments': 78,
      },
    ];
  }

  /// بيانات تجريبية لترتيب الدوري
  static List<Map<String, dynamic>> _getMockStandings() {
    return [
      {
        'position': 1,
        'team': 'الأهلي',
        'played': 15,
        'won': 12,
        'drawn': 2,
        'lost': 1,
        'goalsFor': 35,
        'goalsAgainst': 8,
        'goalDifference': 27,
        'points': 38,
        'logo': '🔴',
      },
      {
        'position': 2,
        'team': 'الزمالك',
        'played': 15,
        'won': 10,
        'drawn': 3,
        'lost': 2,
        'goalsFor': 28,
        'goalsAgainst': 12,
        'goalDifference': 16,
        'points': 33,
        'logo': '⚪',
      },
      {
        'position': 3,
        'team': 'بيراميدز',
        'played': 15,
        'won': 8,
        'drawn': 4,
        'lost': 3,
        'goalsFor': 22,
        'goalsAgainst': 15,
        'goalDifference': 7,
        'points': 28,
        'logo': '🔺',
      },
    ];
  }
}
