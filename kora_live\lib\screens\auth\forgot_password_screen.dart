import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../widgets/custom_text_field.dart';
import '../../widgets/custom_button.dart';

class ForgotPasswordScreen extends StatefulWidget {
  const ForgotPasswordScreen({super.key});

  @override
  State<ForgotPasswordScreen> createState() => _ForgotPasswordScreenState();
}

class _ForgotPasswordScreenState extends State<ForgotPasswordScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  bool _isLoading = false;
  bool _emailSent = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.3), end: Offset.zero).animate(
          CurvedAnimation(
            parent: _animationController,
            curve: Curves.easeOutCubic,
          ),
        );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _emailController.dispose();
    super.dispose();
  }

  void _sendResetEmail() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });

      // Simulate sending reset email
      await Future.delayed(const Duration(seconds: 2));

      if (mounted) {
        setState(() {
          _isLoading = false;
          _emailSent = true;
        });
      }
    }
  }

  void _resendEmail() async {
    setState(() {
      _isLoading = true;
    });

    // Simulate resending email
    await Future.delayed(const Duration(seconds: 1));

    if (mounted) {
      setState(() {
        _isLoading = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم إرسال البريد الإلكتروني مرة أخرى'),
          backgroundColor: AppColors.success,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(gradient: AppColors.darkGradient),
        child: SafeArea(
          child: AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return FadeTransition(
                opacity: _fadeAnimation,
                child: SlideTransition(
                  position: _slideAnimation,
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(AppConstants.defaultPadding),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        // Back Button
                        _buildBackButton(),

                        const SizedBox(height: AppConstants.extraLargePadding),

                        // Header
                        _buildHeader(),

                        const SizedBox(height: AppConstants.extraLargePadding),

                        // Content based on state
                        _emailSent
                            ? _buildSuccessContent()
                            : _buildFormContent(),

                        const SizedBox(height: AppConstants.largePadding),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildBackButton() {
    return Align(
      alignment: Alignment.centerRight,
      child: IconButton(
        onPressed: () => Navigator.of(context).pop(),
        icon: const Icon(Icons.arrow_back_ios, color: AppColors.textLight),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: AppColors.textLight,
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: AppColors.shadowMedium,
                blurRadius: 15,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: Icon(
            _emailSent ? FontAwesomeIcons.circleCheck : FontAwesomeIcons.key,
            size: 35,
            color: _emailSent ? AppColors.success : AppColors.primaryDark,
          ),
        ),
        const SizedBox(height: AppConstants.defaultPadding),
        Text(
          _emailSent ? 'تم الإرسال!' : 'نسيت كلمة المرور؟',
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            color: AppColors.textLight,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: AppConstants.smallPadding),
        Text(
          _emailSent
              ? 'تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني'
              : 'لا تقلق، سنرسل لك رابط إعادة تعيين كلمة المرور',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: AppColors.textLight.withValues(alpha: 0.8),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildFormContent() {
    return Column(
      children: [
        Form(
          key: _formKey,
          child: CustomTextField(
            controller: _emailController,
            labelText: 'البريد الإلكتروني',
            hintText: 'أدخل بريدك الإلكتروني',
            prefixIcon: Icons.email_outlined,
            keyboardType: TextInputType.emailAddress,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'يرجى إدخال البريد الإلكتروني';
              }
              if (!RegExp(AppConstants.emailPattern).hasMatch(value)) {
                return 'يرجى إدخال بريد إلكتروني صحيح';
              }
              return null;
            },
          ),
        ),
        const SizedBox(height: AppConstants.largePadding),
        CustomButton(
          text: 'إرسال رابط الإعادة',
          onPressed: _isLoading ? null : _sendResetEmail,
          isLoading: _isLoading,
          backgroundColor: AppColors.textLight,
          textColor: AppColors.primaryDark,
          icon: FontAwesomeIcons.paperPlane,
        ),
      ],
    );
  }

  Widget _buildSuccessContent() {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(AppConstants.largePadding),
          decoration: BoxDecoration(
            color: AppColors.textLight.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(AppConstants.defaultRadius),
            border: Border.all(
              color: AppColors.textLight.withValues(alpha: 0.3),
            ),
          ),
          child: Column(
            children: [
              Icon(
                FontAwesomeIcons.envelope,
                size: 40,
                color: AppColors.textLight.withValues(alpha: 0.8),
              ),
              const SizedBox(height: AppConstants.defaultPadding),
              Text(
                'تحقق من بريدك الإلكتروني',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  color: AppColors.textLight,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: AppConstants.smallPadding),
              Text(
                _emailController.text,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppColors.textLight.withValues(alpha: 0.8),
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: AppConstants.defaultPadding),
              Text(
                'اتبع التعليمات في البريد الإلكتروني لإعادة تعيين كلمة المرور',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppColors.textLight.withValues(alpha: 0.7),
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
        const SizedBox(height: AppConstants.largePadding),
        CustomButton(
          text: 'إعادة الإرسال',
          onPressed: _isLoading ? null : _resendEmail,
          isLoading: _isLoading,
          backgroundColor: Colors.transparent,
          textColor: AppColors.textLight,
          outlined: true,
          icon: FontAwesomeIcons.arrowRotateRight,
        ),
        const SizedBox(height: AppConstants.defaultPadding),
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(
            'العودة لتسجيل الدخول',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textLight,
              decoration: TextDecoration.underline,
            ),
          ),
        ),
      ],
    );
  }
}
