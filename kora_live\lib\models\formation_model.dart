// نموذج بيانات التشكيلة المهيكلة
class FormationModel {
  final String formation;
  final String team;
  final Map<String, LineData> lines;

  FormationModel({
    required this.formation,
    required this.team,
    required this.lines,
  });

  factory FormationModel.fromJson(Map<String, dynamic> json) {
    return FormationModel(
      formation: json['formation'],
      team: json['team'],
      lines: (json['lines'] as Map<String, dynamic>).map(
        (key, value) => MapEntry(key, LineData.fromJson(value)),
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'formation': formation,
      'team': team,
      'lines': lines.map((key, value) => MapEntry(key, value.toJson())),
    };
  }
}

// بيانات خط اللاعبين
class LineData {
  final List<PlayerData> players;

  LineData({required this.players});

  factory LineData.fromJson(Map<String, dynamic> json) {
    return LineData(
      players: (json['players'] as List)
          .map((player) => PlayerData.fromJson(player))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'players': players.map((player) => player.toJson()).toList(),
    };
  }
}

// بيانات اللاعب
class PlayerData {
  final String name;
  final String number;
  final String rating;
  final String position;
  final String line;
  final String linePosition;
  final String ratingColor;

  PlayerData({
    required this.name,
    required this.number,
    required this.rating,
    required this.position,
    required this.line,
    required this.linePosition,
    required this.ratingColor,
  });

  factory PlayerData.fromJson(Map<String, dynamic> json) {
    return PlayerData(
      name: json['name'],
      number: json['number'],
      rating: json['rating'],
      position: json['position'],
      line: json['line'],
      linePosition: json['linePosition'],
      ratingColor: json['ratingColor'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'number': number,
      'rating': rating,
      'position': position,
      'line': line,
      'linePosition': linePosition,
      'ratingColor': ratingColor,
    };
  }
}

// مثال على البيانات المهيكلة
class FormationExamples {
  // تشكيل 3-4-3 للأردن
  static FormationModel get jordan343 {
    return FormationModel(
      formation: '3-4-3',
      team: 'الأردن',
      lines: {
        'goalkeeper': LineData(
          players: [
            PlayerData(
              name: 'أبو ليلى',
              number: '1',
              rating: '6.5',
              position: 'حارس مرمى',
              line: 'حارس',
              linePosition: 'وسط',
              ratingColor: 'orange',
            ),
          ],
        ),
        'defense': LineData(
          players: [
            PlayerData(
              name: 'نصيف',
              number: '3',
              rating: '6.7',
              position: 'مدافع أيسر',
              line: 'دفاع',
              linePosition: 'يسار',
              ratingColor: 'orange',
            ),
            PlayerData(
              name: 'العربي',
              number: '5',
              rating: '6.7',
              position: 'مدافع وسط',
              line: 'دفاع',
              linePosition: 'وسط',
              ratingColor: 'orange',
            ),
            PlayerData(
              name: 'عبيد',
              number: '16',
              rating: '6.2',
              position: 'مدافع أيمن',
              line: 'دفاع',
              linePosition: 'يمين',
              ratingColor: 'orange',
            ),
          ],
        ),
        'midfield': LineData(
          players: [
            PlayerData(
              name: 'أبو طه',
              number: '20',
              rating: '6.1',
              position: 'جناح أيسر',
              line: 'وسط',
              linePosition: 'يسار',
              ratingColor: 'orange',
            ),
            PlayerData(
              name: 'سعادة',
              number: '15',
              rating: '6.1',
              position: 'وسط دفاعي',
              line: 'وسط',
              linePosition: 'وسط-يسار',
              ratingColor: 'orange',
            ),
            PlayerData(
              name: 'الرشدان',
              number: '21',
              rating: '6.5',
              position: 'وسط هجومي',
              line: 'وسط',
              linePosition: 'وسط-يمين',
              ratingColor: 'orange',
            ),
            PlayerData(
              name: 'عساف',
              number: '17',
              rating: '6.9',
              position: 'جناح أيمن',
              line: 'وسط',
              linePosition: 'يمين',
              ratingColor: 'blue',
            ),
          ],
        ),
        'attack': LineData(
          players: [
            PlayerData(
              name: 'المرضي',
              number: '13',
              rating: '6.7',
              position: 'جناح أيسر',
              line: 'هجوم',
              linePosition: 'يسار',
              ratingColor: 'yellow',
            ),
            PlayerData(
              name: 'التعبان',
              number: '11',
              rating: '6.5',
              position: 'مهاجم وسط',
              line: 'هجوم',
              linePosition: 'وسط',
              ratingColor: 'orange',
            ),
            PlayerData(
              name: 'علوان',
              number: '9',
              rating: '6.2',
              position: 'جناح أيمن',
              line: 'هجوم',
              linePosition: 'يمين',
              ratingColor: 'orange',
            ),
          ],
        ),
      },
    );
  }

  // تشكيل 4-4-2 للعراق (مثال آخر)
  static FormationModel get iraq442 {
    return FormationModel(
      formation: '4-4-2',
      team: 'العراق',
      lines: {
        'goalkeeper': LineData(
          players: [
            PlayerData(
              name: 'جلال حسن',
              number: '1',
              rating: '7.2',
              position: 'حارس مرمى',
              line: 'حارس',
              linePosition: 'وسط',
              ratingColor: 'green',
            ),
          ],
        ),
        'defense': LineData(
          players: [
            PlayerData(
              name: 'أحمد إبراهيم',
              number: '2',
              rating: '6.8',
              position: 'ظهير أيمن',
              line: 'دفاع',
              linePosition: 'يمين',
              ratingColor: 'orange',
            ),
            PlayerData(
              name: 'ريبين سليماني',
              number: '4',
              rating: '7.1',
              position: 'مدافع وسط',
              line: 'دفاع',
              linePosition: 'وسط-يمين',
              ratingColor: 'green',
            ),
            PlayerData(
              name: 'فرانز بوتروس',
              number: '5',
              rating: '6.9',
              position: 'مدافع وسط',
              line: 'دفاع',
              linePosition: 'وسط-يسار',
              ratingColor: 'blue',
            ),
            PlayerData(
              name: 'مصطفى ناظم',
              number: '3',
              rating: '6.6',
              position: 'ظهير أيسر',
              line: 'دفاع',
              linePosition: 'يسار',
              ratingColor: 'orange',
            ),
          ],
        ),
        'midfield': LineData(
          players: [
            PlayerData(
              name: 'أمجد عطوان',
              number: '8',
              rating: '7.3',
              position: 'وسط أيمن',
              line: 'وسط',
              linePosition: 'يمين',
              ratingColor: 'green',
            ),
            PlayerData(
              name: 'إبراهيم بايش',
              number: '6',
              rating: '6.7',
              position: 'وسط دفاعي',
              line: 'وسط',
              linePosition: 'وسط-يمين',
              ratingColor: 'orange',
            ),
            PlayerData(
              name: 'علي عدنان',
              number: '23',
              rating: '6.4',
              position: 'وسط هجومي',
              line: 'وسط',
              linePosition: 'وسط-يسار',
              ratingColor: 'orange',
            ),
            PlayerData(
              name: 'أيمن حسين',
              number: '9',
              rating: '7.0',
              position: 'وسط أيسر',
              line: 'وسط',
              linePosition: 'يسار',
              ratingColor: 'blue',
            ),
          ],
        ),
        'attack': LineData(
          players: [
            PlayerData(
              name: 'عيسى كاشف',
              number: '10',
              rating: '7.5',
              position: 'مهاجم أيمن',
              line: 'هجوم',
              linePosition: 'يمين',
              ratingColor: 'green',
            ),
            PlayerData(
              name: 'مهند علي',
              number: '11',
              rating: '6.8',
              position: 'مهاجم أيسر',
              line: 'هجوم',
              linePosition: 'يسار',
              ratingColor: 'orange',
            ),
          ],
        ),
      },
    );
  }
}
